import { useNavigate } from "react-router-dom"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Presentation, Zap, Download, Users, Clock, Star } from "lucide-react"

export function HomePage() {
  const navigate = useNavigate()

  const features = [
    {
      icon: <Zap className="h-8 w-8 text-yellow-500" />,
      title: "AI-Powered Generation",
      description: "Advanced AI creates professional presentations tailored to your needs"
    },
    {
      icon: <Users className="h-8 w-8 text-green-500" />,
      title: "Audience-Focused",
      description: "Content adapted for your specific audience and presentation context"
    },
    {
      icon: <Clock className="h-8 w-8 text-blue-500" />,
      title: "Lightning Fast",
      description: "Generate complete presentations in under 60 seconds"
    },
    {
      icon: <Download className="h-8 w-8 text-purple-500" />,
      title: "Ready to Use",
      description: "Download professional .pptx files compatible with PowerPoint"
    }
  ]

  return (
    <div className="min-h-[calc(100vh-8rem)] flex flex-col items-center justify-center space-y-12 px-4">
      {/* Hero Section */}
      <div className="text-center space-y-6 max-w-4xl">
        <div className="flex justify-center mb-6">
          <div className="p-4 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20">
            <Presentation className="h-16 w-16 text-blue-600" />
          </div>
        </div>
        
        <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent leading-tight">
          SlideForge
        </h1>
        
        <p className="text-xl md:text-2xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
          Transform your ideas into professional PowerPoint presentations with the power of AI
        </p>
        
        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="ml-2">Trusted by thousands of professionals</span>
        </div>
      </div>

      {/* CTA Button */}
      <div className="space-y-4">
        <Button 
          size="lg" 
          className="text-lg px-8 py-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          onClick={() => navigate("/create")}
        >
          <Presentation className="mr-2 h-5 w-5" />
          Create Presentation
        </Button>
        <p className="text-sm text-muted-foreground text-center">
          No signup required • Generate in seconds
        </p>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl w-full mt-16">
        {features.map((feature, index) => (
          <Card key={index} className="text-center hover:shadow-lg transition-all duration-300 hover:scale-105 bg-card/50 backdrop-blur-sm border-border/50">
            <CardHeader className="pb-4">
              <div className="flex justify-center mb-2">
                {feature.icon}
              </div>
              <CardTitle className="text-lg">{feature.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-sm leading-relaxed">
                {feature.description}
              </CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* How it works */}
      <div className="max-w-4xl w-full mt-16 space-y-8">
        <h2 className="text-3xl font-bold text-center mb-8">How It Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[
            { step: "1", title: "Describe Your Topic", desc: "Tell us what you want to present and who your audience is" },
            { step: "2", title: "AI Creates Content", desc: "Our AI generates professional slides tailored to your needs" },
            { step: "3", title: "Download & Present", desc: "Get your .pptx file ready for PowerPoint or Google Slides" }
          ].map((item, index) => (
            <div key={index} className="text-center space-y-4">
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-lg flex items-center justify-center mx-auto">
                {item.step}
              </div>
              <h3 className="text-xl font-semibold">{item.title}</h3>
              <p className="text-muted-foreground">{item.desc}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}