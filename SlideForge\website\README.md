# SlideForge AI Website

A modern, responsive website for the SlideForge AI presentation generator.

## Features

- **Modern Design**: Clean, professional interface with gradient backgrounds and smooth animations
- **Responsive Layout**: Works perfectly on desktop, tablet, and mobile devices
- **Interactive Demo**: Live demonstration of the AI capabilities
- **Real-time API Integration**: Connects to the Flask backend for live presentation generation
- **Professional UI Components**: Custom modals, progress indicators, and form validation

## Files

- `index.html` - Main website homepage
- `demo.html` - Interactive demo page
- `styles.css` - Additional custom styles and animations
- `README.md` - This documentation

## Setup

### Local Development

1. **Start the Backend Server**:
   ```bash
   cd ../server
   python run.py
   ```

2. **Open the Website**:
   - Open `index.html` in your web browser
   - Or serve it with a local server:
     ```bash
     python -m http.server 8000
     ```
   - Then visit `http://localhost:8000`

### Production Deployment

#### Option 1: Static Hosting (GitHub Pages, Netlify, Vercel)
1. Upload the `website` folder to your hosting service
2. Update the `API_BASE_URL` in `index.html` to point to your deployed backend
3. Deploy the static files

#### Option 2: Full-Stack Deployment (Replit, Heroku, Railway)
1. Use the provided `.replit` configuration
2. Deploy both frontend and backend together
3. The website will automatically connect to the backend

## Configuration

### API Configuration
Update the API base URL in `index.html`:

```javascript
const API_BASE_URL = 'http://localhost:3000'; // Change this to your backend URL
```

For production, change to:
```javascript
const API_BASE_URL = 'https://your-backend-domain.com';
```

### CORS Configuration
Make sure your Flask backend allows requests from your website domain:

```python
from flask_cors import CORS
CORS(app, origins=['https://your-website-domain.com'])
```

## Features Overview

### Homepage (`index.html`)
- Hero section with call-to-action
- Features showcase
- How it works explanation
- Interactive presentation creation form
- Processing modal with real-time progress
- Success modal with download functionality
- Customer testimonials
- Professional footer

### Demo Page (`demo.html`)
- Live demonstration of AI capabilities
- Sample input and output
- Technical features explanation
- Interactive slide generation simulation

### Styling (`styles.css`)
- Custom animations and transitions
- Enhanced hover effects
- Responsive design utilities
- Loading states and progress indicators
- Accessibility improvements

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance

- Optimized images and assets
- Minimal external dependencies
- Efficient CSS and JavaScript
- Fast loading times

## Security

- No sensitive data stored in frontend
- Secure API communication
- Input validation and sanitization
- CORS protection

## Customization

### Colors
The website uses a blue-purple gradient theme. To customize:

1. Update CSS custom properties in the `<style>` section
2. Modify Tailwind classes throughout the HTML
3. Update gradient definitions

### Content
- Update text content directly in the HTML files
- Modify testimonials in the testimonials section
- Change feature descriptions in the features section

### Functionality
- Modify form validation in the JavaScript section
- Update API endpoints and request formats
- Customize modal behavior and animations

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check if the backend server is running
   - Verify the API_BASE_URL is correct
   - Check browser console for CORS errors

2. **Form Not Submitting**
   - Ensure all required fields are filled
   - Check browser console for JavaScript errors
   - Verify form validation logic

3. **Styling Issues**
   - Clear browser cache
   - Check if Tailwind CSS is loading properly
   - Verify custom CSS is not conflicting

### Debug Mode
Enable debug logging by adding to the JavaScript:
```javascript
const DEBUG = true;
if (DEBUG) console.log('Debug info:', data);
```

## Contributing

1. Fork the repository
2. Make your changes
3. Test thoroughly across different browsers
4. Submit a pull request

## License

This project is part of the SlideForge AI application and follows the same license terms.
