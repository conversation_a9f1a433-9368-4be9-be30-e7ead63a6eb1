import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import { ThemeProvider } from "./components/ui/theme-provider"
import { Toaster } from "./components/ui/toaster"
import { Layout } from "./components/Layout"
import { HomePage } from "./pages/HomePage"
import { CreatePresentationPage } from "./pages/CreatePresentationPage"
import { ProcessingPage } from "./pages/ProcessingPage"
import { DownloadPage } from "./pages/DownloadPage"
import { BlankPage } from "./pages/BlankPage"

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="ui-theme">
      <Router>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="/create" element={<CreatePresentationPage />} />
            <Route path="/processing" element={<ProcessingPage />} />
            <Route path="/download/:id" element={<DownloadPage />} />
          </Route>
          <Route path="*" element={<BlankPage />} />
        </Routes>
      </Router>
      <Toaster />
    </ThemeProvider>
  )
}

export default App