#!/usr/bin/env python3
"""
Test script for Gemini AI integration
"""

import os
from dotenv import load_dotenv
import google.generativeai as genai

# Load environment variables
load_dotenv()

def test_gemini_api():
    """Test Gemini API connection"""
    api_key = os.getenv('GEMINI_API_KEY')
    print(f"API Key loaded: {'Yes' if api_key else 'No'}")
    
    if api_key:
        print(f"API Key (first 20 chars): {api_key[:20]}...")
        
        try:
            # Configure Gemini
            genai.configure(api_key=api_key)
            
            # Test with a simple prompt
            model = genai.GenerativeModel('gemini-1.5-flash')
            response = model.generate_content("Say hello and confirm you're working!")
            
            print("✅ Gemini API is working!")
            print(f"Response: {response.text}")
            return True
            
        except Exception as e:
            print(f"❌ Gemini API failed: {e}")
            
            # Try backup API key
            backup_key = "AIzaSyAc71CJZKATT2xidU4zhROEqsFAY73rSXs"
            print(f"\n🔄 Trying backup API key...")
            
            try:
                genai.configure(api_key=backup_key)
                model = genai.GenerativeModel('gemini-1.5-flash')
                response = model.generate_content("Say hello and confirm you're working!")
                
                print("✅ Backup Gemini API is working!")
                print(f"Response: {response.text}")
                
                # Update .env file with working key
                update_env_file(backup_key)
                return True
                
            except Exception as e2:
                print(f"❌ Backup API key also failed: {e2}")
                return False
    else:
        print("❌ No API key found in environment")
        return False

def update_env_file(new_api_key):
    """Update .env file with working API key"""
    try:
        with open('.env', 'r') as f:
            content = f.read()
        
        # Replace the API key line
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('GEMINI_API_KEY='):
                lines[i] = f'GEMINI_API_KEY={new_api_key}'
                break
        
        with open('.env', 'w') as f:
            f.write('\n'.join(lines))
        
        print("✅ Updated .env file with working API key")
        
    except Exception as e:
        print(f"❌ Failed to update .env file: {e}")

if __name__ == '__main__':
    print("Testing Gemini AI Integration...")
    print("=" * 40)
    test_gemini_api()
