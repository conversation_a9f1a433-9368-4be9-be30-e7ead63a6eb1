import { useEffect, useState } from "react"
import { useNavigate, useSearchParams } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Loader2, FileText, Sparkles, Download, CheckCircle } from "lucide-react"
import { checkPresentationStatus } from "@/api/presentations"
import { useToast } from "@/hooks/useToast"

const processingSteps = [
  { id: 1, text: "Analyzing your requirements...", icon: <Sparkles className="h-4 w-4" /> },
  { id: 2, text: "Generating slide content...", icon: <FileText className="h-4 w-4" /> },
  { id: 3, text: "Creating presentation file...", icon: <Download className="h-4 w-4" /> },
  { id: 4, text: "Almost ready...", icon: <CheckCircle className="h-4 w-4" /> },
]

export function ProcessingPage() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { toast } = useToast()
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0)
  const [timeRemaining, setTimeRemaining] = useState(45)
  
  const presentationId = searchParams.get("id")

  useEffect(() => {
    console.log("Processing page loaded with presentation ID:", presentationId)
    
    if (!presentationId) {
      console.error("No presentation ID found, redirecting to home")
      navigate("/")
      return
    }

    // Simulate processing steps
    const stepInterval = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev < processingSteps.length - 1) {
          return prev + 1
        }
        return prev
      })
    }, 8000) // Change step every 8 seconds

    // Update progress
    const progressInterval = setInterval(() => {
      setProgress((prev) => {
        if (prev < 95) {
          return prev + 2
        }
        return prev
      })
    }, 800) // Update progress every 800ms

    // Update time remaining
    const timeInterval = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev > 5) {
          return prev - 1
        }
        return prev
      })
    }, 1000) // Update every second

    // Check presentation status periodically
    const statusInterval = setInterval(async () => {
      try {
        const status = await checkPresentationStatus(presentationId)
        console.log("Presentation status:", status)
        
        if (status.isComplete) {
          console.log("Presentation is complete, navigating to download page")
          navigate(`/download/${presentationId}`)
        }
      } catch (error) {
        console.error("Error checking presentation status:", error)
        toast({
          title: "Error",
          description: "Failed to check presentation status. Please try again.",
          variant: "destructive",
        })
      }
    }, 3000) // Check every 3 seconds

    // Auto-complete after 32 seconds (simulation)
    const completeTimeout = setTimeout(() => {
      console.log("Auto-completing presentation processing")
      setProgress(100)
      setTimeout(() => {
        navigate(`/download/${presentationId}`)
      }, 1000)
    }, 32000)

    return () => {
      clearInterval(stepInterval)
      clearInterval(progressInterval)
      clearInterval(timeInterval)
      clearInterval(statusInterval)
      clearTimeout(completeTimeout)
    }
  }, [presentationId, navigate, toast])

  return (
    <div className="min-h-[calc(100vh-8rem)] flex items-center justify-center py-8">
      <div className="max-w-md w-full mx-auto">
        <Card className="bg-card/50 backdrop-blur-sm border-border/50 shadow-lg">
          <CardHeader className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="p-4 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20">
                <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
              </div>
            </div>
            <CardTitle className="text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Creating Your Presentation
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Progress Bar */}
            <div className="space-y-2">
              <Progress value={progress} className="h-2" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{progress}% complete</span>
                <span>{timeRemaining}s remaining</span>
              </div>
            </div>

            {/* Current Step */}
            <div className="space-y-4">
              {processingSteps.map((step, index) => (
                <div
                  key={step.id}
                  className={`flex items-center gap-3 p-3 rounded-lg transition-all duration-500 ${
                    index === currentStep
                      ? "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800"
                      : index < currentStep
                      ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"
                      : "bg-muted/30"
                  }`}
                >
                  <div
                    className={`flex-shrink-0 ${
                      index === currentStep
                        ? "text-blue-600 animate-pulse"
                        : index < currentStep
                        ? "text-green-600"
                        : "text-muted-foreground"
                    }`}
                  >
                    {index < currentStep ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : index === currentStep ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      step.icon
                    )}
                  </div>
                  <span
                    className={`text-sm ${
                      index === currentStep
                        ? "text-blue-700 dark:text-blue-300 font-medium"
                        : index < currentStep
                        ? "text-green-700 dark:text-green-300"
                        : "text-muted-foreground"
                    }`}
                  >
                    {step.text}
                  </span>
                </div>
              ))}
            </div>

            {/* Info Message */}
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-sm text-muted-foreground">
                Please don't close this page. Your presentation will be ready shortly.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}