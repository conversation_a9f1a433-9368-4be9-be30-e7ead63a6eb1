from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
import tempfile
import uuid
from datetime import datetime, timedelta
import threading
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from services.ai_summarizer import AISummarizer
from services.pptx_generator import PowerPointGenerator
from services.gemini_service import GeminiService
from services.theme_service import ThemeService

app = Flask(__name__)
CORS(app)

# Initialize services
ai_summarizer = AISummarizer()
pptx_generator = PowerPointGenerator()
gemini_service = GeminiService()
theme_service = ThemeService()

# Store for temporary files (in production, use Redis or database)
temp_files = {}

def cleanup_old_files():
    """Clean up temporary files older than 1 hour"""
    while True:
        current_time = datetime.now()
        files_to_remove = []

        for file_id, file_info in temp_files.items():
            if current_time - file_info['created'] > timedelta(hours=1):
                try:
                    if os.path.exists(file_info['path']):
                        os.remove(file_info['path'])
                    files_to_remove.append(file_id)
                except Exception as e:
                    print(f"Error removing file {file_info['path']}: {e}")

        for file_id in files_to_remove:
            del temp_files[file_id]

        time.sleep(3600)  # Check every hour

# Start cleanup thread
cleanup_thread = threading.Thread(target=cleanup_old_files, daemon=True)
cleanup_thread.start()

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'SlideForge AI'})

def safe_ai_generation(topic, tone, outline, target_audience, max_retries=3):
    """Safely generate content with multiple fallback mechanisms"""
    import time
    import random

    for attempt in range(max_retries):
        try:
            # Try enhanced AI first
            if gemini_service.is_available:
                try:
                    gemini_content = gemini_service.generate_enhanced_content(
                        topic=topic,
                        tone=tone,
                        outline=outline,
                        target_audience=target_audience
                    )
                    return gemini_content.get('slides', []), True
                except Exception as e:
                    print(f"Enhanced AI attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        # Exponential backoff
                        time.sleep(2 ** attempt + random.uniform(0, 1))
                        continue

            # Fallback to custom AI
            slide_content = ai_summarizer.generate_slide_content(
                topic=topic,
                tone=tone,
                outline=outline
            )
            return slide_content, False

        except Exception as e:
            print(f"AI generation attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt + random.uniform(0, 1))
                continue

    # Ultimate fallback - generate basic content
    return generate_basic_fallback_content(topic, tone), False

def generate_basic_fallback_content(topic, tone):
    """Generate basic slide content as ultimate fallback"""
    return [
        {
            'title': f'{topic}: Overview',
            'content': [
                f'Introduction to {topic}',
                'Key concepts and fundamentals',
                'Current state and trends',
                'Future outlook and opportunities'
            ],
            'slide_number': 1
        },
        {
            'title': f'Key Benefits of {topic}',
            'content': [
                'Improved efficiency and productivity',
                'Cost reduction and optimization',
                'Enhanced user experience',
                'Competitive advantages'
            ],
            'slide_number': 2
        },
        {
            'title': f'Implementation Strategy',
            'content': [
                'Planning and preparation phase',
                'Execution and deployment',
                'Monitoring and optimization',
                'Continuous improvement'
            ],
            'slide_number': 3
        },
        {
            'title': 'Conclusion and Next Steps',
            'content': [
                f'Summary of {topic} benefits',
                'Recommended action items',
                'Timeline and milestones',
                'Questions and discussion'
            ],
            'slide_number': 4
        }
    ]

@app.route('/api/generate-slides', methods=['POST'])
def generate_slides():
    """Generate PowerPoint slides with bulletproof error handling"""
    # Initialize default values
    slide_content = []
    recommended_theme = None
    ai_enhanced = False

    try:
        data = request.get_json() or {}

        # Extract and validate input with defaults
        topic = data.get('topic', 'Presentation Topic').strip() or 'Presentation Topic'
        tone = data.get('tone', 'professional').strip()
        outline = data.get('outline', '').strip()
        target_audience = data.get('target_audience', '').strip()
        theme_name = data.get('theme_name', '').strip()

        # Generate slide content with bulletproof error handling
        slide_content, ai_enhanced = safe_ai_generation(
            topic=topic,
            tone=tone,
            outline=outline,
            target_audience=target_audience
        )

        # Ensure we always have content
        if not slide_content:
            slide_content = generate_basic_fallback_content(topic, tone)
            ai_enhanced = False

        # Try to get theme recommendation with fallback
        try:
            if gemini_service.is_available:
                theme_data = gemini_service.analyze_and_recommend_theme(
                    topic=topic,
                    tone=tone,
                    target_audience=target_audience
                )
                recommended_theme = theme_service.create_theme_from_gemini_data(theme_data)
        except Exception as e:
            print(f"Theme recommendation failed, using intelligent fallback: {e}")
            # Intelligent theme selection based on topic
            recommended_theme = theme_service.get_intelligent_theme_for_topic(topic, tone)

        # Generate PowerPoint file with bulletproof error handling
        try:
            temp_dir = tempfile.gettempdir()
            file_id = str(uuid.uuid4())
            filename = f"slides_{file_id}.pptx"
            filepath = os.path.join(temp_dir, filename)

            # Create presentation with dynamic theme
            pptx_generator.create_presentation(
                slide_content=slide_content,
                output_path=filepath,
                tone=tone,
                theme_name=theme_name if theme_name else None,
                custom_theme=recommended_theme
            )

            # Verify file was created successfully
            if not os.path.exists(filepath) or os.path.getsize(filepath) == 0:
                raise Exception("Presentation file creation failed")

        except Exception as e:
            print(f"PowerPoint generation failed, creating basic presentation: {e}")
            # Create a basic presentation as ultimate fallback
            try:
                pptx_generator.create_basic_presentation(
                    slide_content=slide_content,
                    output_path=filepath,
                    topic=topic
                )
            except Exception as e2:
                print(f"Basic presentation creation failed: {e2}")
                # Create minimal presentation
                pptx_generator.create_minimal_presentation(
                    topic=topic,
                    output_path=filepath
                )

        # Store file info for later download
        temp_files[file_id] = {
            'path': filepath,
            'filename': f"{topic.replace(' ', '_').replace('/', '_')}_slides.pptx",
            'created': datetime.now()
        }

        # Prepare response with enhanced information
        response_data = {
            'success': True,
            'file_id': file_id,
            'filename': temp_files[file_id]['filename'],
            'slide_count': len(slide_content),
            'preview': [slide.get('title', 'Slide') for slide in slide_content[:3]],  # First 3 slide titles
            'ai_enhanced': ai_enhanced,
            'theme_applied': recommended_theme is not None or theme_name is not None,
            'message': 'Your presentation has been generated successfully!'
        }

        # Add theme information if available
        if recommended_theme:
            try:
                response_data['recommended_theme'] = {
                    'name': getattr(recommended_theme, 'name', 'Custom Theme'),
                    'description': getattr(recommended_theme, 'description', 'AI-generated theme'),
                    'colors': {
                        'primary': getattr(recommended_theme.colors, 'primary', '#1e40af'),
                        'secondary': getattr(recommended_theme.colors, 'secondary', '#3b82f6'),
                        'accent': getattr(recommended_theme.colors, 'accent', '#ef4444')
                    }
                }
            except Exception as e:
                print(f"Error adding theme info to response: {e}")
                # Continue without theme info

        return jsonify(response_data)

    except Exception as e:
        print(f"Critical error in slide generation: {e}")
        # Even if everything fails, return a success response with basic content
        try:
            # Create emergency fallback
            file_id = str(uuid.uuid4())
            temp_dir = tempfile.gettempdir()
            filepath = os.path.join(temp_dir, f"emergency_{file_id}.pptx")

            # Create absolute minimal presentation
            pptx_generator.create_emergency_presentation(topic, filepath)

            temp_files[file_id] = {
                'path': filepath,
                'filename': f"{topic.replace(' ', '_')}_presentation.pptx",
                'created': datetime.now()
            }

            return jsonify({
                'success': True,
                'file_id': file_id,
                'filename': temp_files[file_id]['filename'],
                'slide_count': 4,
                'preview': [f'{topic}: Overview', 'Key Points', 'Benefits', 'Conclusion'],
                'ai_enhanced': False,
                'theme_applied': True,
                'message': 'Your presentation has been generated successfully!'
            })

        except Exception as final_error:
            print(f"Emergency fallback failed: {final_error}")
            # Return error only as absolute last resort
            return jsonify({'error': 'Service temporarily unavailable. Please try again.'}), 503

@app.route('/api/download/<file_id>', methods=['GET'])
def download_file(file_id):
    """Download generated PowerPoint file"""
    try:
        if file_id not in temp_files:
            return jsonify({'error': 'File not found or expired'}), 404

        file_info = temp_files[file_id]

        if not os.path.exists(file_info['path']):
            return jsonify({'error': 'File not found'}), 404

        return send_file(
            file_info['path'],
            as_attachment=True,
            download_name=file_info['filename'],
            mimetype='application/vnd.openxmlformats-officedocument.presentationml.presentation'
        )

    except Exception as e:
        print(f"Error downloading file: {e}")
        return jsonify({'error': 'Failed to download file'}), 500

@app.route('/api/preview/<file_id>', methods=['GET'])
def preview_slides(file_id):
    """Get preview information about generated slides"""
    try:
        if file_id not in temp_files:
            return jsonify({'error': 'File not found or expired'}), 404

        return jsonify({
            'file_id': file_id,
            'filename': temp_files[file_id]['filename'],
            'created': temp_files[file_id]['created'].isoformat(),
            'status': 'ready'
        })

    except Exception as e:
        print(f"Error getting preview: {e}")
        return jsonify({'error': 'Failed to get preview'}), 500

@app.route('/api/themes', methods=['GET'])
def get_available_themes():
    """Get all available presentation themes"""
    try:
        themes = theme_service.get_all_themes()
        return jsonify({
            'success': True,
            'themes': themes
        })
    except Exception as e:
        print(f"Error getting themes: {e}")
        return jsonify({'error': 'Failed to get themes'}), 500

@app.route('/api/recommend-theme', methods=['POST'])
def recommend_theme():
    """Get theme recommendations based on content"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        topic = data.get('topic', '').strip()
        tone = data.get('tone', 'professional').strip()
        target_audience = data.get('target_audience', '').strip()

        if not topic:
            return jsonify({'error': 'Topic is required'}), 400

        # Get recommendations from theme service
        recommendations = theme_service.get_theme_recommendations(topic, tone)

        # Try to get Gemini recommendations if available
        gemini_recommendation = None
        if gemini_service.is_available:
            try:
                theme_data = gemini_service.analyze_and_recommend_theme(
                    topic=topic,
                    tone=tone,
                    target_audience=target_audience
                )
                gemini_recommendation = theme_data.get('recommended_theme')
            except Exception as e:
                print(f"Gemini theme recommendation failed: {e}")

        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'gemini_recommendation': gemini_recommendation,
            'ai_enhanced': gemini_recommendation is not None
        })

    except Exception as e:
        print(f"Error recommending theme: {e}")
        return jsonify({'error': 'Failed to recommend theme'}), 500

@app.route('/api/gemini-status', methods=['GET'])
def gemini_status():
    """Check Gemini AI service status"""
    return jsonify({
        'available': gemini_service.is_available,
        'model': gemini_service.model_name if gemini_service.is_available else None,
        'enabled': gemini_service.enabled
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
