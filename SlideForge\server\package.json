{"name": "node_server", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.27.3", "axios": "^1.7.7", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "chart.js": "^4.4.1", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.4.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-session": "^1.18.0", "moment": "^2.30.1", "mongoose": "^8.1.1", "openai": "^4.63.0", "pino": "^9.5.0", "jsonwebtoken": "^9.0.2"}}