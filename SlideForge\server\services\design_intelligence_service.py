#!/usr/bin/env python3
"""
Design Intelligence Service for Project Slidra
Implements AI-driven presentation design intelligence for optimal layouts and composition
"""

from typing import List, Dict, Tuple, Optional
import logging

class DesignIntelligenceService:
    """Service for AI-driven presentation design optimization"""
    
    def __init__(self):
        # Layout templates based on content type and complexity
        self.layout_templates = {
            'title_slide': {
                'elements': ['title', 'subtitle', 'background_image'],
                'composition': 'centered',
                'image_prominence': 'high'
            },
            'text_heavy': {
                'elements': ['title', 'bullet_points', 'side_image'],
                'composition': 'left_aligned',
                'image_prominence': 'low'
            },
            'balanced': {
                'elements': ['title', 'content', 'top_image'],
                'composition': 'hierarchical',
                'image_prominence': 'medium'
            },
            'visual_focus': {
                'elements': ['title', 'large_image', 'minimal_text'],
                'composition': 'image_dominant',
                'image_prominence': 'high'
            },
            'data_visualization': {
                'elements': ['title', 'chart', 'supporting_text'],
                'composition': 'data_centric',
                'image_prominence': 'none'
            }
        }
        
        # Content analysis patterns
        self.content_patterns = {
            'introduction': ['introduction', 'overview', 'welcome', 'agenda'],
            'data_heavy': ['statistics', 'numbers', 'percentage', 'data', 'metrics'],
            'process': ['step', 'process', 'workflow', 'methodology', 'approach'],
            'comparison': ['versus', 'compare', 'difference', 'alternative', 'option'],
            'conclusion': ['conclusion', 'summary', 'takeaway', 'next steps', 'action']
        }
        
        # Visual hierarchy rules
        self.hierarchy_rules = {
            'title_prominence': {
                'font_size_ratio': 1.8,  # Relative to body text
                'color_contrast': 'high',
                'positioning': 'top_prominent'
            },
            'content_flow': {
                'bullet_spacing': 'generous',
                'line_height': 1.3,
                'paragraph_spacing': 'medium'
            },
            'visual_balance': {
                'white_space_ratio': 0.3,  # 30% white space
                'element_alignment': 'consistent',
                'color_distribution': 'balanced'
            }
        }

    def analyze_slide_content(self, slide_data: Dict) -> Dict:
        """Analyze slide content to determine optimal design approach"""
        title = slide_data.get('title', '').lower()
        content = slide_data.get('content', [])
        slide_number = slide_data.get('slide_number', 1)
        
        # Calculate content metrics
        content_metrics = self._calculate_content_metrics(title, content)
        
        # Determine content type
        content_type = self._determine_content_type(title, content)
        
        # Assess complexity
        complexity_level = self._assess_complexity(content_metrics)
        
        # Determine optimal layout
        optimal_layout = self._determine_optimal_layout(
            content_type, complexity_level, slide_number, content_metrics
        )
        
        return {
            'content_type': content_type,
            'complexity_level': complexity_level,
            'optimal_layout': optimal_layout,
            'content_metrics': content_metrics,
            'design_recommendations': self._generate_design_recommendations(
                content_type, complexity_level, optimal_layout
            )
        }

    def _calculate_content_metrics(self, title: str, content: List[str]) -> Dict:
        """Calculate various metrics about the slide content"""
        total_words = len(title.split()) + sum(len(point.split()) for point in content)
        total_characters = len(title) + sum(len(point) for point in content)
        bullet_count = len(content)
        
        # Average words per bullet point
        avg_words_per_bullet = (
            sum(len(point.split()) for point in content) / bullet_count 
            if bullet_count > 0 else 0
        )
        
        # Content density score
        density_score = total_words / max(bullet_count, 1)
        
        return {
            'total_words': total_words,
            'total_characters': total_characters,
            'bullet_count': bullet_count,
            'avg_words_per_bullet': avg_words_per_bullet,
            'density_score': density_score,
            'title_length': len(title.split())
        }

    def _determine_content_type(self, title: str, content: List[str]) -> str:
        """Determine the type of content based on title and bullet points"""
        text_content = f"{title} {' '.join(content)}".lower()
        
        # Check for specific patterns
        for content_type, patterns in self.content_patterns.items():
            if any(pattern in text_content for pattern in patterns):
                return content_type
        
        # Default categorization based on content characteristics
        if any(word in text_content for word in ['chart', 'graph', 'data', 'statistics']):
            return 'data_heavy'
        elif any(word in text_content for word in ['process', 'step', 'workflow']):
            return 'process'
        elif len(content) <= 3:
            return 'visual_focus'
        else:
            return 'balanced'

    def _assess_complexity(self, content_metrics: Dict) -> str:
        """Assess the complexity level of the content"""
        total_words = content_metrics['total_words']
        bullet_count = content_metrics['bullet_count']
        density_score = content_metrics['density_score']
        
        # Calculate complexity score
        complexity_score = 0
        
        # Word count factor
        if total_words > 80:
            complexity_score += 3
        elif total_words > 50:
            complexity_score += 2
        elif total_words > 25:
            complexity_score += 1
        
        # Bullet count factor
        if bullet_count > 6:
            complexity_score += 2
        elif bullet_count > 4:
            complexity_score += 1
        
        # Density factor
        if density_score > 15:
            complexity_score += 2
        elif density_score > 10:
            complexity_score += 1
        
        # Determine complexity level
        if complexity_score >= 6:
            return 'high'
        elif complexity_score >= 3:
            return 'medium'
        else:
            return 'low'

    def _determine_optimal_layout(self, content_type: str, complexity_level: str, 
                                slide_number: int, content_metrics: Dict) -> str:
        """Determine the optimal layout based on analysis"""
        
        # Title slide gets special treatment
        if slide_number == 1:
            return 'title_slide'
        
        # High complexity content needs text-focused layout
        if complexity_level == 'high':
            return 'text_heavy'
        
        # Low complexity can be more visual
        if complexity_level == 'low':
            if content_type in ['introduction', 'conclusion']:
                return 'visual_focus'
            else:
                return 'balanced'
        
        # Medium complexity - choose based on content type
        if content_type == 'data_heavy':
            return 'data_visualization'
        elif content_type == 'process':
            return 'balanced'
        elif content_type in ['introduction', 'conclusion']:
            return 'visual_focus'
        else:
            return 'balanced'

    def _generate_design_recommendations(self, content_type: str, 
                                       complexity_level: str, layout: str) -> Dict:
        """Generate specific design recommendations"""
        recommendations = {
            'typography': self._get_typography_recommendations(complexity_level),
            'spacing': self._get_spacing_recommendations(complexity_level),
            'color_usage': self._get_color_recommendations(content_type),
            'image_strategy': self._get_image_recommendations(layout, content_type),
            'visual_hierarchy': self._get_hierarchy_recommendations(layout)
        }
        
        return recommendations

    def _get_typography_recommendations(self, complexity_level: str) -> Dict:
        """Get typography recommendations based on complexity"""
        if complexity_level == 'high':
            return {
                'title_size': 32,
                'body_size': 18,
                'line_height': 1.2,
                'font_weight': 'normal'
            }
        elif complexity_level == 'medium':
            return {
                'title_size': 36,
                'body_size': 20,
                'line_height': 1.3,
                'font_weight': 'normal'
            }
        else:  # low complexity
            return {
                'title_size': 40,
                'body_size': 24,
                'line_height': 1.4,
                'font_weight': 'bold'
            }

    def _get_spacing_recommendations(self, complexity_level: str) -> Dict:
        """Get spacing recommendations based on complexity"""
        if complexity_level == 'high':
            return {
                'bullet_spacing': 8,
                'paragraph_spacing': 12,
                'margin_size': 'compact'
            }
        elif complexity_level == 'medium':
            return {
                'bullet_spacing': 12,
                'paragraph_spacing': 16,
                'margin_size': 'standard'
            }
        else:  # low complexity
            return {
                'bullet_spacing': 16,
                'paragraph_spacing': 20,
                'margin_size': 'generous'
            }

    def _get_color_recommendations(self, content_type: str) -> Dict:
        """Get color usage recommendations based on content type"""
        color_schemes = {
            'introduction': {'accent_usage': 'high', 'contrast': 'medium'},
            'data_heavy': {'accent_usage': 'low', 'contrast': 'high'},
            'process': {'accent_usage': 'medium', 'contrast': 'medium'},
            'conclusion': {'accent_usage': 'high', 'contrast': 'medium'},
            'balanced': {'accent_usage': 'medium', 'contrast': 'medium'}
        }
        
        return color_schemes.get(content_type, color_schemes['balanced'])

    def _get_image_recommendations(self, layout: str, content_type: str) -> Dict:
        """Get image usage recommendations"""
        image_strategies = {
            'title_slide': {'size': 'large', 'position': 'background', 'opacity': 0.7},
            'text_heavy': {'size': 'small', 'position': 'side', 'opacity': 1.0},
            'balanced': {'size': 'medium', 'position': 'top', 'opacity': 1.0},
            'visual_focus': {'size': 'large', 'position': 'center', 'opacity': 1.0},
            'data_visualization': {'size': 'none', 'position': 'none', 'opacity': 0}
        }
        
        return image_strategies.get(layout, image_strategies['balanced'])

    def _get_hierarchy_recommendations(self, layout: str) -> Dict:
        """Get visual hierarchy recommendations"""
        hierarchy_configs = {
            'title_slide': {'title_prominence': 'very_high', 'content_emphasis': 'low'},
            'text_heavy': {'title_prominence': 'medium', 'content_emphasis': 'high'},
            'balanced': {'title_prominence': 'high', 'content_emphasis': 'medium'},
            'visual_focus': {'title_prominence': 'high', 'content_emphasis': 'low'},
            'data_visualization': {'title_prominence': 'medium', 'content_emphasis': 'very_high'}
        }
        
        return hierarchy_configs.get(layout, hierarchy_configs['balanced'])

    def optimize_slide_flow(self, all_slides: List[Dict]) -> List[Dict]:
        """Optimize the flow and progression of slides"""
        optimized_slides = []
        
        for i, slide in enumerate(all_slides):
            # Analyze current slide
            analysis = self.analyze_slide_content(slide)
            
            # Consider context from previous and next slides
            context = self._analyze_slide_context(all_slides, i)
            
            # Apply flow optimizations
            optimized_slide = self._apply_flow_optimizations(slide, analysis, context)
            
            optimized_slides.append(optimized_slide)
        
        return optimized_slides

    def _analyze_slide_context(self, all_slides: List[Dict], current_index: int) -> Dict:
        """Analyze the context of a slide within the presentation"""
        total_slides = len(all_slides)
        position = 'beginning' if current_index < 2 else 'end' if current_index >= total_slides - 2 else 'middle'
        
        # Analyze content progression
        prev_slide = all_slides[current_index - 1] if current_index > 0 else None
        next_slide = all_slides[current_index + 1] if current_index < total_slides - 1 else None
        
        return {
            'position': position,
            'slide_index': current_index,
            'total_slides': total_slides,
            'previous_slide': prev_slide,
            'next_slide': next_slide
        }

    def _apply_flow_optimizations(self, slide: Dict, analysis: Dict, context: Dict) -> Dict:
        """Apply flow-based optimizations to a slide"""
        optimized_slide = slide.copy()
        
        # Add transition recommendations
        optimized_slide['transition_style'] = self._recommend_transition(analysis, context)
        
        # Add pacing recommendations
        optimized_slide['pacing'] = self._recommend_pacing(analysis, context)
        
        # Add emphasis recommendations
        optimized_slide['emphasis'] = self._recommend_emphasis(analysis, context)
        
        return optimized_slide

    def _recommend_transition(self, analysis: Dict, context: Dict) -> str:
        """Recommend transition style based on content and context"""
        content_type = analysis['content_type']
        position = context['position']
        
        if position == 'beginning':
            return 'fade_in'
        elif position == 'end':
            return 'fade_out'
        elif content_type == 'data_heavy':
            return 'slide_left'
        else:
            return 'fade'

    def _recommend_pacing(self, analysis: Dict, context: Dict) -> Dict:
        """Recommend pacing for slide presentation"""
        complexity = analysis['complexity_level']
        
        pacing_map = {
            'low': {'duration': 30, 'auto_advance': True},
            'medium': {'duration': 45, 'auto_advance': False},
            'high': {'duration': 60, 'auto_advance': False}
        }
        
        return pacing_map.get(complexity, pacing_map['medium'])

    def _recommend_emphasis(self, analysis: Dict, context: Dict) -> Dict:
        """Recommend emphasis techniques"""
        content_type = analysis['content_type']
        
        emphasis_map = {
            'introduction': {'highlight_key_points': True, 'use_animations': True},
            'data_heavy': {'highlight_key_points': False, 'use_animations': False},
            'conclusion': {'highlight_key_points': True, 'use_animations': True},
            'balanced': {'highlight_key_points': True, 'use_animations': False}
        }
        
        return emphasis_map.get(content_type, emphasis_map['balanced'])
