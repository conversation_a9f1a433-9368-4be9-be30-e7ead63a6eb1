#!/usr/bin/env python3
"""
Test script for SlideForge AI application
Tests the AI summarizer, PowerPoint generator, and API endpoints
"""

import unittest
import tempfile
import os
import json
from app import app
from services.ai_summarizer import AISummarizer
from services.pptx_generator import PowerPointGenerator

class TestAISummarizer(unittest.TestCase):
    """Test the custom AI summarizer"""
    
    def setUp(self):
        self.summarizer = AISummarizer()
    
    def test_preprocess_text(self):
        """Test text preprocessing"""
        text = "  This is a   test with   extra spaces!  "
        result = self.summarizer.preprocess_text(text)
        self.assertEqual(result, "This is a test with extra spaces!")
    
    def test_extract_key_phrases(self):
        """Test key phrase extraction"""
        text = "Machine learning and artificial intelligence are transforming business operations"
        phrases = self.summarizer.extract_key_phrases(text, max_phrases=5)
        self.assertIsInstance(phrases, list)
        self.assertGreater(len(phrases), 0)
    
    def test_detect_topic_category(self):
        """Test topic category detection"""
        business_topic = "marketing strategy revenue growth"
        category = self.summarizer.detect_topic_category(business_topic, "")
        self.assertIn(category, ['business', 'technology', 'education', 'research'])
        
        tech_topic = "software development architecture"
        category = self.summarizer.detect_topic_category(tech_topic, "")
        self.assertIn(category, ['business', 'technology', 'education', 'research'])
    
    def test_generate_slide_content(self):
        """Test slide content generation"""
        slides = self.summarizer.generate_slide_content(
            topic="Digital Marketing Strategies",
            tone="professional",
            outline="Introduction\nSEO Techniques\nSocial Media\nConclusion"
        )
        
        self.assertIsInstance(slides, list)
        self.assertGreater(len(slides), 0)
        
        # Check slide structure
        for slide in slides:
            self.assertIn('title', slide)
            self.assertIn('content', slide)
            self.assertIn('slide_number', slide)
            self.assertIsInstance(slide['content'], list)

class TestPowerPointGenerator(unittest.TestCase):
    """Test the PowerPoint generator"""
    
    def setUp(self):
        self.generator = PowerPointGenerator()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_create_presentation(self):
        """Test PowerPoint presentation creation"""
        slide_content = [
            {
                'title': 'Introduction',
                'content': ['Welcome to our presentation', 'Overview of topics'],
                'slide_number': 1
            },
            {
                'title': 'Main Content',
                'content': ['Key point 1', 'Key point 2', 'Key point 3'],
                'slide_number': 2
            }
        ]
        
        output_path = os.path.join(self.temp_dir, 'test_presentation.pptx')
        result_path = self.generator.create_presentation(
            slide_content=slide_content,
            output_path=output_path,
            tone='professional'
        )
        
        self.assertEqual(result_path, output_path)
        self.assertTrue(os.path.exists(output_path))
        self.assertGreater(os.path.getsize(output_path), 0)

class TestFlaskAPI(unittest.TestCase):
    """Test the Flask API endpoints"""
    
    def setUp(self):
        self.app = app.test_client()
        self.app.testing = True
    
    def test_health_endpoint(self):
        """Test health check endpoint"""
        response = self.app.get('/api/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')
        self.assertEqual(data['service'], 'SlideForge AI')
    
    def test_generate_slides_endpoint(self):
        """Test slide generation endpoint"""
        test_data = {
            'topic': 'Test Presentation Topic',
            'tone': 'professional',
            'outline': 'Introduction\nMain Content\nConclusion'
        }
        
        response = self.app.post('/api/generate-slides', 
                               data=json.dumps(test_data),
                               content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('file_id', data)
        self.assertIn('filename', data)
        self.assertIn('slide_count', data)
        self.assertGreater(data['slide_count'], 0)
    
    def test_generate_slides_missing_topic(self):
        """Test slide generation with missing topic"""
        test_data = {
            'tone': 'professional',
            'outline': 'Some outline'
        }
        
        response = self.app.post('/api/generate-slides',
                               data=json.dumps(test_data),
                               content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        
        data = json.loads(response.data)
        self.assertIn('error', data)
    
    def test_download_nonexistent_file(self):
        """Test downloading a non-existent file"""
        response = self.app.get('/api/download/nonexistent-id')
        self.assertEqual(response.status_code, 404)
    
    def test_preview_nonexistent_file(self):
        """Test preview of a non-existent file"""
        response = self.app.get('/api/preview/nonexistent-id')
        self.assertEqual(response.status_code, 404)

def run_integration_test():
    """Run an integration test of the complete workflow"""
    print("\n" + "="*50)
    print("RUNNING INTEGRATION TEST")
    print("="*50)
    
    # Test the complete workflow
    summarizer = AISummarizer()
    generator = PowerPointGenerator()
    
    # Generate slide content
    print("1. Generating slide content...")
    slides = summarizer.generate_slide_content(
        topic="Artificial Intelligence in Healthcare",
        tone="professional",
        outline="Introduction\nCurrent Applications\nBenefits and Challenges\nFuture Prospects\nConclusion"
    )
    
    print(f"   Generated {len(slides)} slides")
    for i, slide in enumerate(slides[:3]):  # Show first 3 slides
        print(f"   Slide {i+1}: {slide['title']}")
    
    # Generate PowerPoint
    print("2. Creating PowerPoint presentation...")
    temp_dir = tempfile.mkdtemp()
    output_path = os.path.join(temp_dir, 'integration_test.pptx')
    
    generator.create_presentation(
        slide_content=slides,
        output_path=output_path,
        tone='professional'
    )
    
    file_size = os.path.getsize(output_path)
    print(f"   Created presentation: {file_size} bytes")
    
    # Clean up
    os.remove(output_path)
    os.rmdir(temp_dir)
    
    print("3. Integration test completed successfully!")
    print("="*50)

if __name__ == '__main__':
    print("SlideForge AI - Test Suite")
    print("="*50)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration test
    run_integration_test()
