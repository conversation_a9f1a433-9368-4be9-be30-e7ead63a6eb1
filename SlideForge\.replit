modules = ["python-3.11", "nodejs-20"]

[nix]
channel = "stable-24_05"

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.backend"

[[workflows.workflow.tasks]]
task = "workflow.frontend"

[[workflows.workflow]]
name = "backend"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd server && python -m pip install -r requirements.txt && python run.py"

[[workflows.workflow]]
name = "frontend"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd client && npm install && npm run dev"

[deployment]
run = ["sh", "-c", "cd server && python run.py"]

[[ports]]
localPort = 5000
externalPort = 80
exposeLocalhost = true

[[ports]]
localPort = 5173
externalPort = 3000
exposeLocalhost = true
