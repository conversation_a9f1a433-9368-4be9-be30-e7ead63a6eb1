from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE
import os

class PowerPointGenerator:
    """Generate PowerPoint presentations with custom styling"""
    
    def __init__(self):
        self.color_schemes = {
            'professional': {
                'primary': RGBColor(31, 73, 125),      # Dark blue
                'secondary': RGBColor(79, 129, 189),    # Light blue
                'accent': RGBColor(192, 80, 77),        # Red accent
                'text': RGBColor(64, 64, 64),           # Dark gray
                'background': RGBColor(255, 255, 255)   # White
            },
            'casual': {
                'primary': RGBColor(46, 125, 50),       # Green
                'secondary': RGBColor(102, 187, 106),   # Light green
                'accent': RGBColor(255, 152, 0),        # Orange
                'text': RGBColor(33, 33, 33),           # Almost black
                'background': RGBColor(250, 250, 250)   # Off-white
            },
            'academic': {
                'primary': RGBColor(26, 35, 126),       # Deep blue
                'secondary': RGBColor(63, 81, 181),     # Indigo
                'accent': RGBColor(121, 85, 72),        # Brown
                'text': RGBColor(33, 33, 33),           # Dark gray
                'background': RGBColor(255, 255, 255)   # White
            },
            'creative': {
                'primary': RGBColor(142, 36, 170),      # Purple
                'secondary': RGBColor(186, 104, 200),   # Light purple
                'accent': RGBColor(255, 193, 7),        # Amber
                'text': RGBColor(66, 66, 66),           # Medium gray
                'background': RGBColor(248, 248, 248)   # Light gray
            }
        }
    
    def create_presentation(self, slide_content, output_path, tone='professional'):
        """Create a PowerPoint presentation from slide content"""
        # Create presentation
        prs = Presentation()
        
        # Set slide dimensions (16:9 aspect ratio)
        prs.slide_width = Inches(13.33)
        prs.slide_height = Inches(7.5)
        
        # Get color scheme
        colors = self.color_schemes.get(tone, self.color_schemes['professional'])
        
        # Create title slide
        self.create_title_slide(prs, slide_content, colors)
        
        # Create content slides
        for slide_data in slide_content[1:]:  # Skip first slide as it's usually intro
            self.create_content_slide(prs, slide_data, colors)
        
        # Save presentation
        prs.save(output_path)
        return output_path
    
    def create_title_slide(self, prs, slide_content, colors):
        """Create the title slide"""
        # Use blank layout
        slide_layout = prs.slide_layouts[6]  # Blank layout
        slide = prs.slides.add_slide(slide_layout)
        
        # Set background color
        background = slide.background
        fill = background.fill
        fill.solid()
        fill.fore_color.rgb = colors['background']
        
        # Add title
        title_box = slide.shapes.add_textbox(
            Inches(1), Inches(2), Inches(11.33), Inches(2)
        )
        title_frame = title_box.text_frame
        title_frame.text = slide_content[0]['title'] if slide_content else "Presentation"
        
        # Style title
        title_paragraph = title_frame.paragraphs[0]
        title_paragraph.alignment = PP_ALIGN.CENTER
        title_run = title_paragraph.runs[0]
        title_run.font.name = 'Calibri'
        title_run.font.size = Pt(44)
        title_run.font.bold = True
        title_run.font.color.rgb = colors['primary']
        
        # Add subtitle if available
        if slide_content and slide_content[0].get('content'):
            subtitle_text = slide_content[0]['content'][0] if slide_content[0]['content'] else ""
            subtitle_box = slide.shapes.add_textbox(
                Inches(2), Inches(4.5), Inches(9.33), Inches(1)
            )
            subtitle_frame = subtitle_box.text_frame
            subtitle_frame.text = subtitle_text
            
            subtitle_paragraph = subtitle_frame.paragraphs[0]
            subtitle_paragraph.alignment = PP_ALIGN.CENTER
            subtitle_run = subtitle_paragraph.runs[0]
            subtitle_run.font.name = 'Calibri'
            subtitle_run.font.size = Pt(24)
            subtitle_run.font.color.rgb = colors['secondary']
        
        # Add decorative shape
        shape = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE,
            Inches(0), Inches(6.8), Inches(13.33), Inches(0.7)
        )
        shape.fill.solid()
        shape.fill.fore_color.rgb = colors['primary']
        shape.line.fill.background()
    
    def create_content_slide(self, prs, slide_data, colors):
        """Create a content slide with title and bullet points"""
        # Use blank layout
        slide_layout = prs.slide_layouts[6]  # Blank layout
        slide = prs.slides.add_slide(slide_layout)
        
        # Set background color
        background = slide.background
        fill = background.fill
        fill.solid()
        fill.fore_color.rgb = colors['background']
        
        # Add header bar
        header_shape = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE,
            Inches(0), Inches(0), Inches(13.33), Inches(1.2)
        )
        header_shape.fill.solid()
        header_shape.fill.fore_color.rgb = colors['primary']
        header_shape.line.fill.background()
        
        # Add title
        title_box = slide.shapes.add_textbox(
            Inches(0.5), Inches(0.2), Inches(12.33), Inches(0.8)
        )
        title_frame = title_box.text_frame
        title_frame.text = slide_data['title']
        title_frame.margin_left = Inches(0.2)
        title_frame.margin_top = Inches(0.1)
        title_frame.vertical_anchor = MSO_ANCHOR.MIDDLE
        
        # Style title
        title_paragraph = title_frame.paragraphs[0]
        title_paragraph.alignment = PP_ALIGN.LEFT
        title_run = title_paragraph.runs[0]
        title_run.font.name = 'Calibri'
        title_run.font.size = Pt(32)
        title_run.font.bold = True
        title_run.font.color.rgb = RGBColor(255, 255, 255)  # White text
        
        # Add content area
        content_box = slide.shapes.add_textbox(
            Inches(1), Inches(1.8), Inches(11.33), Inches(5)
        )
        content_frame = content_box.text_frame
        content_frame.margin_left = Inches(0.2)
        content_frame.margin_top = Inches(0.2)
        
        # Add bullet points
        if slide_data.get('content'):
            for i, bullet_point in enumerate(slide_data['content']):
                if i == 0:
                    # First paragraph
                    p = content_frame.paragraphs[0]
                else:
                    # Add new paragraph
                    p = content_frame.add_paragraph()
                
                p.text = bullet_point
                p.level = 0
                
                # Style bullet point
                run = p.runs[0]
                run.font.name = 'Calibri'
                run.font.size = Pt(20)
                run.font.color.rgb = colors['text']
                
                # Add spacing between bullet points
                p.space_after = Pt(12)
        
        # Add accent line at bottom
        accent_shape = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE,
            Inches(1), Inches(6.8), Inches(11.33), Inches(0.1)
        )
        accent_shape.fill.solid()
        accent_shape.fill.fore_color.rgb = colors['accent']
        accent_shape.line.fill.background()
        
        # Add slide number
        slide_num_box = slide.shapes.add_textbox(
            Inches(11.5), Inches(7), Inches(1.5), Inches(0.4)
        )
        slide_num_frame = slide_num_box.text_frame
        slide_num_frame.text = str(slide_data.get('slide_number', ''))
        
        slide_num_paragraph = slide_num_frame.paragraphs[0]
        slide_num_paragraph.alignment = PP_ALIGN.RIGHT
        slide_num_run = slide_num_paragraph.runs[0]
        slide_num_run.font.name = 'Calibri'
        slide_num_run.font.size = Pt(12)
        slide_num_run.font.color.rgb = colors['secondary']
