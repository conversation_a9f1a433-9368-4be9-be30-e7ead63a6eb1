import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Download, FileText, CheckCircle, ArrowLeft, RefreshCw } from "lucide-react"
import { downloadPresentation, getPreview } from "@/api/presentations"
import { useToast } from "@/hooks/useToast"

interface PreviewData {
  file_id: string;
  filename: string;
  created: string;
  status: string;
}

export function DownloadPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [isDownloading, setIsDownloading] = useState(false)
  const [previewData, setPreviewData] = useState<PreviewData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!id) {
      navigate("/")
      return
    }

    loadPreview()
  }, [id, navigate])

  const loadPreview = async () => {
    if (!id) return

    try {
      setIsLoading(true)
      const preview = await getPreview(id)
      setPreviewData(preview)
    } catch (error: any) {
      console.error("Error loading preview:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to load presentation preview",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDownload = async () => {
    if (!id) return

    try {
      setIsDownloading(true)
      const { blob, filename } = await downloadPresentation(id)
      
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast({
        title: "Success",
        description: "Presentation downloaded successfully!",
      })
    } catch (error: any) {
      console.error("Error downloading presentation:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to download presentation",
        variant: "destructive",
      })
    } finally {
      setIsDownloading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-[calc(100vh-8rem)] flex items-center justify-center py-8">
        <div className="max-w-md w-full mx-auto">
          <Card className="bg-card/50 backdrop-blur-sm border-border/50 shadow-lg">
            <CardContent className="flex items-center justify-center py-8">
              <div className="flex items-center gap-3">
                <RefreshCw className="h-5 w-5 animate-spin text-blue-600" />
                <span className="text-muted-foreground">Loading presentation...</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-[calc(100vh-8rem)] py-8">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/")}
            className="hover:bg-muted/50"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>

        <Card className="bg-card/50 backdrop-blur-sm border-border/50 shadow-lg">
          <CardHeader className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="p-4 rounded-full bg-gradient-to-br from-green-100 to-blue-100 dark:from-green-900/20 dark:to-blue-900/20">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </div>
            <CardTitle className="text-2xl bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
              Presentation Ready!
            </CardTitle>
            <CardDescription className="text-base">
              Your presentation has been generated successfully and is ready for download.
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Presentation Info */}
            {previewData && (
              <div className="bg-muted/30 rounded-lg p-4 space-y-3">
                <div className="flex items-center gap-3">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <h3 className="font-medium">{previewData.filename}</h3>
                    <p className="text-sm text-muted-foreground">
                      Created: {new Date(previewData.created).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Download Button */}
            <Button
              onClick={handleDownload}
              disabled={isDownloading}
              size="lg"
              className="w-full h-12 text-base bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {isDownloading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Downloading...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Download Presentation
                </>
              )}
            </Button>

            {/* Additional Actions */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => navigate("/create")}
                className="flex-1"
              >
                Create Another
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate("/")}
                className="flex-1"
              >
                Go Home
              </Button>
            </div>

            {/* Tips */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                Tips for your presentation:
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• Review and customize the content to match your specific needs</li>
                <li>• Add your own images and branding for a professional look</li>
                <li>• Practice your presentation to ensure smooth delivery</li>
                <li>• Consider your audience and adjust the tone if needed</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
