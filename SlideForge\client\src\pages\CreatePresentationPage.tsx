import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Sparkles, Users, MessageSquare, FileText } from "lucide-react"
import { createPresentation } from "@/api/presentations"
import { useToast } from "@/hooks/useToast"

const formSchema = z.object({
  topic: z.string().min(5, "Topic must be at least 5 characters long"),
  audience: z.string().min(1, "Please select an audience type"),
  customAudience: z.string().optional(),
  tone: z.string().min(1, "Please select a tone"),
  outline: z.string().optional(),
})

type FormData = z.infer<typeof formSchema>

export function CreatePresentationPage() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      topic: "",
      audience: "",
      customAudience: "",
      tone: "",
      outline: "",
    },
  })

  const watchedAudience = form.watch("audience")

  const onSubmit = async (data: FormData) => {
    console.log("Form submission started with data:", data)
    setIsSubmitting(true)
    
    try {
      const response = await createPresentation({
        topic: data.topic,
        audience: data.audience === "other" ? data.customAudience || "" : data.audience,
        tone: data.tone,
        outline: data.outline || "",
      })
      
      console.log("Presentation creation response:", response)
      
      // Navigate to processing page with the presentation ID
      navigate(`/processing?id=${response.presentationId}`)
    } catch (error) {
      console.error("Error creating presentation:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create presentation. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-[calc(100vh-8rem)] py-8">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/")}
            className="hover:bg-muted/50"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>

        <Card className="bg-card/50 backdrop-blur-sm border-border/50 shadow-lg">
          <CardHeader className="text-center space-y-2">
            <div className="flex justify-center mb-4">
              <div className="p-3 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20">
                <Sparkles className="h-8 w-8 text-blue-600" />
              </div>
            </div>
            <CardTitle className="text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Create Your Presentation
            </CardTitle>
            <CardDescription className="text-base">
              Tell us about your presentation and we'll generate professional slides for you
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Topic Field */}
                <FormField
                  control={form.control}
                  name="topic"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2 text-base font-medium">
                        <FileText className="h-4 w-4 text-blue-600" />
                        Presentation Topic *
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Digital Marketing Strategies for Small Businesses"
                          className="h-12 text-base"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Describe the main subject of your presentation
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Audience Field */}
                <FormField
                  control={form.control}
                  name="audience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2 text-base font-medium">
                        <Users className="h-4 w-4 text-green-600" />
                        Target Audience *
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-12 text-base">
                            <SelectValue placeholder="Select your audience type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="business-executives">Business Executives</SelectItem>
                          <SelectItem value="students-academic">Students/Academic</SelectItem>
                          <SelectItem value="general-public">General Public</SelectItem>
                          <SelectItem value="technical-engineering">Technical/Engineering</SelectItem>
                          <SelectItem value="sales-team">Sales Team</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose the primary audience for your presentation
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Custom Audience Field */}
                {watchedAudience === "other" && (
                  <FormField
                    control={form.control}
                    name="customAudience"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Specify Your Audience</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Healthcare professionals, Non-profit volunteers"
                            className="h-12 text-base"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Tone Field */}
                <FormField
                  control={form.control}
                  name="tone"
                  render={({ field }) => (
                    <FormItem className="space-y-4">
                      <FormLabel className="flex items-center gap-2 text-base font-medium">
                        <MessageSquare className="h-4 w-4 text-purple-600" />
                        Presentation Tone *
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="grid grid-cols-1 md:grid-cols-2 gap-4"
                        >
                          {[
                            { value: "professional-formal", label: "Professional/Formal", desc: "Business-focused and authoritative" },
                            { value: "conversational-casual", label: "Conversational/Casual", desc: "Friendly and approachable" },
                            { value: "persuasive-sales", label: "Persuasive/Sales", desc: "Compelling and action-oriented" },
                            { value: "educational-informative", label: "Educational/Informative", desc: "Clear and instructional" }
                          ].map((option) => (
                            <div key={option.value} className="flex items-start space-x-3 p-4 rounded-lg border hover:bg-muted/50 transition-colors">
                              <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                              <div className="space-y-1 flex-1">
                                <Label htmlFor={option.value} className="font-medium cursor-pointer">
                                  {option.label}
                                </Label>
                                <p className="text-sm text-muted-foreground">{option.desc}</p>
                              </div>
                            </div>
                          ))}
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Outline Field */}
                <FormField
                  control={form.control}
                  name="outline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-medium">
                        Outline (Optional)
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="• Introduction to the topic&#10;• Key benefits and features&#10;• Implementation strategies&#10;• Conclusion and next steps"
                          className="min-h-[120px] text-base resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide bullet points or structure you'd like included (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Submit Button */}
                <Button
                  type="submit"
                  size="lg"
                  disabled={isSubmitting}
                  className="w-full h-12 text-base bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating Presentation...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      Generate Presentation
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}