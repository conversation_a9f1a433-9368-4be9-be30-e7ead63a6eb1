import os
import json
import logging
from typing import Dict, List, Optional, Tuple
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

class GeminiService:
    """Enhanced content generation service using Google Gemini AI"""
    
    def __init__(self):
        self.api_key = os.getenv('GEMINI_API_KEY')
        self.model_name = os.getenv('GEMINI_MODEL', 'gemini-1.5-flash')
        self.enabled = os.getenv('ENABLE_GEMINI', 'True').lower() == 'true'
        self.backup_api_key = "AIzaSyAc71CJZKATT2xidU4zhROEqsFAY73rSXs"

        if self.enabled and self.api_key:
            try:
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel(self.model_name)
                self.is_available = True
                logging.info(f"Gemini AI initialized successfully with model: {self.model_name}")
            except Exception as e:
                logging.error(f"Failed to initialize Gemini AI with primary key: {e}")
                # Try backup API key
                try:
                    logging.info("Attempting to use backup API key...")
                    genai.configure(api_key=self.backup_api_key)
                    self.model = genai.GenerativeModel(self.model_name)
                    self.is_available = True
                    self.api_key = self.backup_api_key
                    logging.info(f"Gemini AI initialized successfully with backup key")
                except Exception as e2:
                    logging.error(f"Failed to initialize Gemini AI with backup key: {e2}")
                    self.is_available = False
        else:
            self.is_available = False
            logging.warning("Gemini AI disabled or API key not provided")
    
    def generate_enhanced_content(self, topic: str, tone: str, outline: str = "", 
                                target_audience: str = "") -> Dict:
        """Generate enhanced slide content using Gemini AI"""
        if not self.is_available:
            raise Exception("Gemini AI service not available")
        
        try:
            # Create comprehensive prompt for content generation
            prompt = self._create_content_prompt(topic, tone, outline, target_audience)
            
            # Configure safety settings
            safety_settings = {
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
            
            # Generate content
            response = self.model.generate_content(
                prompt,
                safety_settings=safety_settings,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    top_p=0.8,
                    top_k=40,
                    max_output_tokens=2048,
                )
            )
            
            # Parse the response
            content_data = self._parse_content_response(response.text)
            return content_data
            
        except Exception as e:
            logging.error(f"Error generating content with Gemini: {e}")
            raise
    
    def analyze_and_recommend_theme(self, topic: str, tone: str, target_audience: str = "") -> Dict:
        """Analyze content and recommend appropriate visual theme"""
        if not self.is_available:
            raise Exception("Gemini AI service not available")
        
        try:
            prompt = self._create_theme_analysis_prompt(topic, tone, target_audience)
            
            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.3,  # Lower temperature for more consistent theme recommendations
                    top_p=0.8,
                    max_output_tokens=1024,
                )
            )
            
            theme_data = self._parse_theme_response(response.text)
            return theme_data
            
        except Exception as e:
            logging.error(f"Error analyzing theme with Gemini: {e}")
            raise
    
    def _create_content_prompt(self, topic: str, tone: str, outline: str, target_audience: str) -> str:
        """Create a comprehensive prompt for content generation"""
        prompt = f"""
You are an expert presentation designer and content creator. Generate professional slide content for a presentation with the following specifications:

**Topic**: {topic}
**Tone**: {tone}
**Target Audience**: {target_audience or "General professional audience"}
**Outline**: {outline or "Please create an appropriate structure"}

Please generate a comprehensive presentation with 5-8 slides. For each slide, provide:
1. A clear, engaging title
2. 3-4 bullet points with substantial, valuable content
3. Key insights or takeaways
4. Ensure content is appropriate for the specified tone and audience

Format your response as a JSON object with this structure:
{{
    "slides": [
        {{
            "title": "Slide Title",
            "content": [
                "First bullet point with detailed information",
                "Second bullet point with valuable insights",
                "Third bullet point with actionable content"
            ],
            "slide_number": 1,
            "notes": "Optional speaker notes or additional context"
        }}
    ],
    "presentation_summary": {{
        "total_slides": 6,
        "estimated_duration": "15-20 minutes",
        "key_themes": ["theme1", "theme2", "theme3"]
    }}
}}

Guidelines:
- Make content engaging and informative
- Ensure logical flow between slides
- Include actionable insights where appropriate
- Adapt language and complexity to the target audience
- For {tone} tone: {"Use formal, professional language with data-driven insights" if tone == "professional" else "Use conversational, accessible language" if tone == "casual" else "Use scholarly, detailed language with references" if tone == "academic" else "Use engaging, innovative language with creative examples"}
"""
        return prompt
    
    def _create_theme_analysis_prompt(self, topic: str, tone: str, target_audience: str) -> str:
        """Create prompt for theme analysis and recommendation"""
        prompt = f"""
You are a professional presentation designer specializing in visual themes and aesthetics. Analyze the following presentation requirements and recommend the most appropriate visual theme:

**Topic**: {topic}
**Tone**: {tone}
**Target Audience**: {target_audience or "General professional audience"}

Based on this information, recommend a visual theme that includes:
1. Primary and secondary color schemes
2. Font recommendations
3. Layout style
4. Visual elements and imagery suggestions
5. Overall design approach

Consider factors like:
- Industry standards and expectations
- Audience preferences and context
- Topic complexity and formality
- Brand perception and professionalism

Format your response as a JSON object:
{{
    "recommended_theme": {{
        "name": "Theme Name",
        "description": "Brief description of the theme approach",
        "color_scheme": {{
            "primary": "#hexcolor",
            "secondary": "#hexcolor",
            "accent": "#hexcolor",
            "text": "#hexcolor",
            "background": "#hexcolor"
        }},
        "typography": {{
            "heading_font": "Font Name",
            "body_font": "Font Name",
            "font_style": "modern/classic/creative"
        }},
        "layout_style": "minimalist/corporate/creative/academic",
        "visual_elements": [
            "element1",
            "element2",
            "element3"
        ]
    }},
    "alternative_themes": [
        {{
            "name": "Alternative Theme Name",
            "color_scheme": {{"primary": "#hexcolor", "secondary": "#hexcolor"}},
            "description": "Brief description"
        }}
    ],
    "reasoning": "Explanation of why this theme is recommended for this specific presentation"
}}
"""
        return prompt
    
    def _parse_content_response(self, response_text: str) -> Dict:
        """Parse Gemini's content generation response"""
        try:
            # Clean the response text
            cleaned_text = response_text.strip()
            
            # Remove markdown code blocks if present
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]
            
            # Parse JSON
            content_data = json.loads(cleaned_text)
            
            # Validate structure
            if 'slides' not in content_data:
                raise ValueError("Invalid response structure: missing 'slides'")
            
            return content_data
            
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse Gemini content response: {e}")
            # Fallback: try to extract content manually
            return self._extract_content_fallback(response_text)
        except Exception as e:
            logging.error(f"Error parsing content response: {e}")
            raise
    
    def _parse_theme_response(self, response_text: str) -> Dict:
        """Parse Gemini's theme recommendation response"""
        try:
            # Clean the response text
            cleaned_text = response_text.strip()
            
            # Remove markdown code blocks if present
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]
            
            # Parse JSON
            theme_data = json.loads(cleaned_text)
            
            # Validate structure
            if 'recommended_theme' not in theme_data:
                raise ValueError("Invalid theme response structure")
            
            return theme_data
            
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse Gemini theme response: {e}")
            # Return default theme as fallback
            return self._get_default_theme()
        except Exception as e:
            logging.error(f"Error parsing theme response: {e}")
            return self._get_default_theme()
    
    def _extract_content_fallback(self, response_text: str) -> Dict:
        """Fallback method to extract content when JSON parsing fails"""
        # Simple fallback - create basic structure
        lines = response_text.split('\n')
        slides = []
        current_slide = None
        
        for line in lines:
            line = line.strip()
            if line.startswith('#') or line.startswith('**'):
                # Potential slide title
                if current_slide:
                    slides.append(current_slide)
                current_slide = {
                    'title': line.replace('#', '').replace('**', '').strip(),
                    'content': [],
                    'slide_number': len(slides) + 1
                }
            elif line.startswith('-') or line.startswith('•'):
                # Bullet point
                if current_slide:
                    current_slide['content'].append(line[1:].strip())
        
        if current_slide:
            slides.append(current_slide)
        
        return {
            'slides': slides,
            'presentation_summary': {
                'total_slides': len(slides),
                'estimated_duration': '10-15 minutes',
                'key_themes': ['content', 'presentation', 'information']
            }
        }
    
    def _get_default_theme(self) -> Dict:
        """Return default theme when Gemini analysis fails"""
        return {
            'recommended_theme': {
                'name': 'Professional Blue',
                'description': 'Clean, professional theme suitable for business presentations',
                'color_scheme': {
                    'primary': '#1e40af',
                    'secondary': '#3b82f6',
                    'accent': '#ef4444',
                    'text': '#374151',
                    'background': '#ffffff'
                },
                'typography': {
                    'heading_font': 'Calibri',
                    'body_font': 'Calibri',
                    'font_style': 'modern'
                },
                'layout_style': 'corporate',
                'visual_elements': ['clean lines', 'minimal graphics', 'professional icons']
            },
            'alternative_themes': [],
            'reasoning': 'Default professional theme selected as fallback'
        }
