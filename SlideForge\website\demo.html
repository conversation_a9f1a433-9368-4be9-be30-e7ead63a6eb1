<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SlideForge AI - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-blue-600">SlideForge AI</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-blue-600">Back to Home</a>
                    <button onclick="runDemo()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        Run Demo
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Demo Section -->
    <div class="max-w-6xl mx-auto px-4 py-12">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">SlideForge AI Demo</h1>
            <p class="text-xl text-gray-600">See how our AI generates professional presentations</p>
        </div>

        <div class="grid lg:grid-cols-2 gap-12">
            <!-- Input Section -->
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-semibold mb-6">Demo Input</h2>
                
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Topic</label>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-gray-800">"Artificial Intelligence in Healthcare"</p>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tone</label>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-gray-800">Professional</p>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Outline</label>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-gray-800">
                                • Introduction<br>
                                • Current Applications<br>
                                • Benefits and Challenges<br>
                                • Future Prospects<br>
                                • Conclusion
                            </p>
                        </div>
                    </div>
                </div>
                
                <button onclick="runDemo()" class="w-full mt-6 bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition duration-300">
                    Generate Demo Presentation
                </button>
            </div>

            <!-- Output Section -->
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-semibold mb-6">Generated Slides</h2>
                
                <div id="demoOutput" class="space-y-4">
                    <div class="text-center text-gray-500 py-12">
                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p>Click "Generate Demo Presentation" to see the AI-generated slides</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Showcase -->
        <div class="mt-16 bg-white rounded-xl shadow-lg p-8">
            <h2 class="text-2xl font-semibold mb-8 text-center">AI Model Features</h2>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">TF-IDF Analysis</h3>
                    <p class="text-gray-600">Extracts key phrases using Term Frequency-Inverse Document Frequency</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Topic Classification</h3>
                    <p class="text-gray-600">Automatically categorizes content for appropriate slide structure</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Content Generation</h3>
                    <p class="text-gray-600">Creates contextual bullet points and slide titles</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const demoSlides = [
            {
                title: "Introduction",
                content: [
                    "Overview of Artificial Intelligence in Healthcare",
                    "Importance and relevance in modern medicine",
                    "What we'll cover today"
                ]
            },
            {
                title: "Current Applications",
                content: [
                    "Medical imaging and diagnostics",
                    "Drug discovery and development",
                    "Personalized treatment plans",
                    "Robotic surgery assistance"
                ]
            },
            {
                title: "Benefits and Challenges",
                content: [
                    "Improved diagnostic accuracy",
                    "Reduced healthcare costs",
                    "Data privacy concerns",
                    "Integration with existing systems"
                ]
            },
            {
                title: "Future Prospects",
                content: [
                    "Predictive healthcare analytics",
                    "AI-powered virtual assistants",
                    "Precision medicine advancement",
                    "Global healthcare accessibility"
                ]
            },
            {
                title: "Conclusion",
                content: [
                    "Summary of key benefits",
                    "Addressing implementation challenges",
                    "The future of AI in healthcare"
                ]
            }
        ];

        function runDemo() {
            const outputDiv = document.getElementById('demoOutput');
            
            // Show loading state
            outputDiv.innerHTML = `
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">AI is analyzing and generating slides...</p>
                </div>
            `;
            
            // Simulate processing time
            setTimeout(() => {
                let slidesHTML = '';
                
                demoSlides.forEach((slide, index) => {
                    slidesHTML += `
                        <div class="border rounded-lg p-4 bg-gray-50">
                            <h3 class="font-semibold text-lg mb-3 text-blue-600">Slide ${index + 1}: ${slide.title}</h3>
                            <ul class="space-y-2">
                                ${slide.content.map(point => `<li class="flex items-start"><span class="text-blue-500 mr-2">•</span><span class="text-gray-700">${point}</span></li>`).join('')}
                            </ul>
                        </div>
                    `;
                });
                
                slidesHTML += `
                    <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-green-800 font-medium">Demo presentation generated successfully!</span>
                        </div>
                        <p class="text-green-700 text-sm mt-2">In the real application, this would be converted to a downloadable .pptx file.</p>
                    </div>
                `;
                
                outputDiv.innerHTML = slidesHTML;
            }, 3000);
        }
    </script>
</body>
</html>
