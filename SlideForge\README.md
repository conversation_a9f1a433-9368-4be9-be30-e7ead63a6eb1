# SlideForge AI - PowerPoint Generator

A full-stack web application that generates professional PowerPoint presentations using a custom AI model built from scratch. No external APIs like OpenAI or Hugging Face are used - all AI logic is implemented using traditional ML libraries.

## Features

- **Custom AI Text Summarizer**: Built with scikit-learn and NumPy for intelligent content generation
- **Professional PowerPoint Generation**: Creates styled .pptx files using python-pptx
- **Modern React Frontend**: Built with Vite, TypeScript, and Tailwind CSS
- **Flask Backend**: RESTful API with file handling and temporary storage
- **Multiple Presentation Tones**: Professional, casual, academic, and creative styles
- **Intelligent Content Structuring**: Automatically organizes content based on topic category
- **Secure File Downloads**: Temporary file storage with automatic cleanup

## Technology Stack

### Backend
- **Python 3.11** with Flask
- **scikit-learn** for machine learning features
- **NumPy** for numerical computations
- **python-pptx** for PowerPoint generation
- **NLTK & TextBlob** for natural language processing

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Radix UI** for accessible components
- **React Hook Form** with <PERSON>od validation

## Getting Started

### Prerequisites
- Python 3.11+
- Node.js 20+
- npm or yarn

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SlideForge
   ```

2. **Set up the backend**
   ```bash
   cd server
   pip install -r requirements.txt
   cp .env.example .env
   python run.py
   ```

3. **Set up the frontend**
   ```bash
   cd client
   npm install
   npm run dev
   ```

4. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000

### Deployment on Replit

1. **Import the project** to Replit
2. **Run the project** - Replit will automatically:
   - Install Python and Node.js dependencies
   - Start both backend and frontend servers
   - Configure port forwarding

3. **Access your deployed app** through the Replit URL

## API Endpoints

### Health Check
- `GET /api/health` - Check if the service is running

### Slide Generation
- `POST /api/generate-slides` - Generate PowerPoint slides
  ```json
  {
    "topic": "Digital Marketing Strategies",
    "tone": "professional",
    "outline": "Introduction\nKey strategies\nImplementation\nConclusion"
  }
  ```

### File Operations
- `GET /api/download/{file_id}` - Download generated presentation
- `GET /api/preview/{file_id}` - Get presentation preview info

## AI Model Details

The custom AI summarizer uses a hybrid approach:

1. **Rule-based Processing**: 
   - Topic categorization (business, technology, education, research)
   - Template-based structure generation
   - Tone adjustment algorithms

2. **Machine Learning Features**:
   - TF-IDF vectorization for key phrase extraction
   - Cosine similarity for content relevance
   - K-means clustering for content organization

3. **Natural Language Processing**:
   - NLTK for tokenization and preprocessing
   - TextBlob for sentiment and linguistic analysis
   - Custom algorithms for bullet point generation

## Project Structure

```
SlideForge/
├── server/                 # Python Flask backend
│   ├── services/          # AI and PowerPoint services
│   │   ├── ai_summarizer.py
│   │   └── pptx_generator.py
│   ├── app.py            # Flask application
│   ├── run.py            # Entry point
│   └── requirements.txt  # Python dependencies
├── client/               # React frontend
│   ├── src/
│   │   ├── api/         # API integration
│   │   ├── components/  # React components
│   │   ├── pages/       # Application pages
│   │   └── lib/         # Utilities
│   └── package.json     # Node.js dependencies
├── .replit              # Replit configuration
└── README.md           # This file
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with modern web technologies and traditional ML approaches
- Designed for educational purposes and professional use
- No external AI APIs required - fully self-contained solution
