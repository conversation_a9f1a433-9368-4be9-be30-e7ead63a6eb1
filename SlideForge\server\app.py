from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
import tempfile
import uuid
from datetime import datetime, timedelta
import threading
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from services.ai_summarizer import AISummarizer
from services.pptx_generator import PowerPointGenerator
from services.gemini_service import GeminiService
from services.theme_service import ThemeService

app = Flask(__name__)
CORS(app)

# Initialize services
ai_summarizer = AISummarizer()
pptx_generator = PowerPointGenerator()
gemini_service = GeminiService()
theme_service = ThemeService()

# Store for temporary files (in production, use Redis or database)
temp_files = {}

def cleanup_old_files():
    """Clean up temporary files older than 1 hour"""
    while True:
        current_time = datetime.now()
        files_to_remove = []
        
        for file_id, file_info in temp_files.items():
            if current_time - file_info['created'] > timedelta(hours=1):
                try:
                    if os.path.exists(file_info['path']):
                        os.remove(file_info['path'])
                    files_to_remove.append(file_id)
                except Exception as e:
                    print(f"Error removing file {file_info['path']}: {e}")
        
        for file_id in files_to_remove:
            del temp_files[file_id]
        
        time.sleep(3600)  # Check every hour

# Start cleanup thread
cleanup_thread = threading.Thread(target=cleanup_old_files, daemon=True)
cleanup_thread.start()

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'SlideForge AI'})

@app.route('/api/generate-slides', methods=['POST'])
def generate_slides():
    """Generate PowerPoint slides with enhanced AI and dynamic themes"""
    try:
        data = request.get_json()

        # Validate input
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        topic = data.get('topic', '').strip()
        tone = data.get('tone', 'professional').strip()
        outline = data.get('outline', '').strip()
        target_audience = data.get('target_audience', '').strip()
        theme_name = data.get('theme_name', '').strip()
        use_gemini = data.get('use_gemini', True)

        if not topic:
            return jsonify({'error': 'Topic is required'}), 400

        # Generate slide content using enhanced AI
        slide_content = None
        recommended_theme = None

        # Try Gemini first if enabled and available
        if use_gemini and gemini_service.is_available:
            try:
                # Generate enhanced content with Gemini
                gemini_content = gemini_service.generate_enhanced_content(
                    topic=topic,
                    tone=tone,
                    outline=outline,
                    target_audience=target_audience
                )
                slide_content = gemini_content.get('slides', [])

                # Get theme recommendation from Gemini
                theme_data = gemini_service.analyze_and_recommend_theme(
                    topic=topic,
                    tone=tone,
                    target_audience=target_audience
                )
                recommended_theme = theme_service.create_theme_from_gemini_data(theme_data)

            except Exception as e:
                print(f"Gemini AI failed, falling back to custom AI: {e}")
                # Fallback to custom AI
                slide_content = ai_summarizer.generate_slide_content(
                    topic=topic,
                    tone=tone,
                    outline=outline
                )
        else:
            # Use custom AI summarizer
            slide_content = ai_summarizer.generate_slide_content(
                topic=topic,
                tone=tone,
                outline=outline
            )
        
        # Generate PowerPoint file with enhanced features
        temp_dir = tempfile.gettempdir()
        file_id = str(uuid.uuid4())
        filename = f"slides_{file_id}.pptx"
        filepath = os.path.join(temp_dir, filename)

        # Create presentation with dynamic theme
        pptx_generator.create_presentation(
            slide_content=slide_content,
            output_path=filepath,
            tone=tone,
            theme_name=theme_name if theme_name else None,
            custom_theme=recommended_theme
        )
        
        # Store file info for later download
        temp_files[file_id] = {
            'path': filepath,
            'filename': f"{topic.replace(' ', '_')}_slides.pptx",
            'created': datetime.now()
        }
        
        # Prepare response with enhanced information
        response_data = {
            'success': True,
            'file_id': file_id,
            'filename': temp_files[file_id]['filename'],
            'slide_count': len(slide_content),
            'preview': [slide['title'] for slide in slide_content[:3]],  # First 3 slide titles
            'ai_enhanced': use_gemini and gemini_service.is_available,
            'theme_applied': recommended_theme is not None or theme_name is not None
        }

        # Add theme information if available
        if recommended_theme:
            response_data['recommended_theme'] = {
                'name': recommended_theme.name,
                'description': recommended_theme.description,
                'colors': {
                    'primary': recommended_theme.colors.primary,
                    'secondary': recommended_theme.colors.secondary,
                    'accent': recommended_theme.colors.accent
                }
            }

        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error generating slides: {e}")
        return jsonify({'error': 'Failed to generate slides'}), 500

@app.route('/api/download/<file_id>', methods=['GET'])
def download_file(file_id):
    """Download generated PowerPoint file"""
    try:
        if file_id not in temp_files:
            return jsonify({'error': 'File not found or expired'}), 404
        
        file_info = temp_files[file_id]
        
        if not os.path.exists(file_info['path']):
            return jsonify({'error': 'File not found'}), 404
        
        return send_file(
            file_info['path'],
            as_attachment=True,
            download_name=file_info['filename'],
            mimetype='application/vnd.openxmlformats-officedocument.presentationml.presentation'
        )
        
    except Exception as e:
        print(f"Error downloading file: {e}")
        return jsonify({'error': 'Failed to download file'}), 500

@app.route('/api/preview/<file_id>', methods=['GET'])
def preview_slides(file_id):
    """Get preview information about generated slides"""
    try:
        if file_id not in temp_files:
            return jsonify({'error': 'File not found or expired'}), 404
        
        # In a real implementation, you might extract slide previews
        # For now, return basic info
        return jsonify({
            'file_id': file_id,
            'filename': temp_files[file_id]['filename'],
            'created': temp_files[file_id]['created'].isoformat(),
            'status': 'ready'
        })
        
    except Exception as e:
        print(f"Error getting preview: {e}")
        return jsonify({'error': 'Failed to get preview'}), 500

@app.route('/api/themes', methods=['GET'])
def get_available_themes():
    """Get all available presentation themes"""
    try:
        themes = theme_service.get_all_themes()
        return jsonify({
            'success': True,
            'themes': themes
        })
    except Exception as e:
        print(f"Error getting themes: {e}")
        return jsonify({'error': 'Failed to get themes'}), 500

@app.route('/api/recommend-theme', methods=['POST'])
def recommend_theme():
    """Get theme recommendations based on content"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        topic = data.get('topic', '').strip()
        tone = data.get('tone', 'professional').strip()
        target_audience = data.get('target_audience', '').strip()

        if not topic:
            return jsonify({'error': 'Topic is required'}), 400

        # Get recommendations from theme service
        recommendations = theme_service.get_theme_recommendations(topic, tone)

        # Try to get Gemini recommendations if available
        gemini_recommendation = None
        if gemini_service.is_available:
            try:
                theme_data = gemini_service.analyze_and_recommend_theme(
                    topic=topic,
                    tone=tone,
                    target_audience=target_audience
                )
                gemini_recommendation = theme_data.get('recommended_theme')
            except Exception as e:
                print(f"Gemini theme recommendation failed: {e}")

        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'gemini_recommendation': gemini_recommendation,
            'ai_enhanced': gemini_recommendation is not None
        })

    except Exception as e:
        print(f"Error recommending theme: {e}")
        return jsonify({'error': 'Failed to recommend theme'}), 500

@app.route('/api/gemini-status', methods=['GET'])
def gemini_status():
    """Check Gemini AI service status"""
    return jsonify({
        'available': gemini_service.is_available,
        'model': gemini_service.model_name if gemini_service.is_available else None,
        'enabled': gemini_service.enabled
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
