import api from './api';

export interface CreatePresentationRequest {
  topic: string;
  audience?: string;
  tone: string;
  outline?: string;
}

export interface CreatePresentationResponse {
  success: boolean;
  file_id: string;
  filename: string;
  slide_count: number;
  preview: string[];
  presentationId?: string; // For compatibility with existing frontend
}

export interface DownloadResponse {
  blob: Blob;
  filename: string;
}

export const createPresentation = async (data: CreatePresentationRequest): Promise<CreatePresentationResponse> => {
  try {
    // Map the frontend tone values to backend tone values
    const toneMapping: { [key: string]: string } = {
      'professional-formal': 'professional',
      'conversational-casual': 'casual',
      'persuasive-sales': 'professional',
      'educational-informative': 'academic'
    };

    const requestData = {
      topic: data.topic,
      tone: toneMapping[data.tone] || 'professional',
      outline: data.outline || ''
    };

    const response = await api.post('/api/generate-slides', requestData);
    
    // Add presentationId for compatibility with existing frontend
    const result = {
      ...response.data,
      presentationId: response.data.file_id
    };

    return result;
  } catch (error: any) {
    console.error('Error creating presentation:', error);
    
    if (error.response?.data?.error) {
      throw new Error(error.response.data.error);
    }
    
    throw new Error('Failed to create presentation. Please try again.');
  }
};

export const downloadPresentation = async (fileId: string): Promise<DownloadResponse> => {
  try {
    const response = await api.get(`/api/download/${fileId}`, {
      responseType: 'blob'
    });

    // Get filename from Content-Disposition header or use default
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'presentation.pptx';
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    return {
      blob: response.data,
      filename
    };
  } catch (error: any) {
    console.error('Error downloading presentation:', error);
    
    if (error.response?.data?.error) {
      throw new Error(error.response.data.error);
    }
    
    throw new Error('Failed to download presentation. Please try again.');
  }
};

export const getPreview = async (fileId: string) => {
  try {
    const response = await api.get(`/api/preview/${fileId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting preview:', error);
    
    if (error.response?.data?.error) {
      throw new Error(error.response.data.error);
    }
    
    throw new Error('Failed to get presentation preview.');
  }
};

export const checkPresentationStatus = async (fileId: string) => {
  try {
    const response = await api.get(`/api/preview/${fileId}`);
    return {
      isComplete: response.data.status === 'ready',
      ...response.data
    };
  } catch (error: any) {
    console.error('Error checking presentation status:', error);

    if (error.response?.status === 404) {
      throw new Error('Presentation not found or expired');
    }

    if (error.response?.data?.error) {
      throw new Error(error.response.data.error);
    }

    throw new Error('Failed to check presentation status.');
  }
};

export const checkHealth = async () => {
  try {
    const response = await api.get('/api/health');
    return response.data;
  } catch (error) {
    console.error('Health check failed:', error);
    throw new Error('Backend service is not available');
  }
};
