import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from pptx.dml.color import RGBColor

@dataclass
class ThemeColors:
    """Data class for theme color schemes"""
    primary: str
    secondary: str
    accent: str
    text: str
    background: str
    
    def to_rgb(self, color_hex: str) -> RGBColor:
        """Convert hex color to RGBColor object"""
        hex_color = color_hex.lstrip('#')
        return RGBColor(
            int(hex_color[0:2], 16),
            int(hex_color[2:4], 16),
            int(hex_color[4:6], 16)
        )

@dataclass
class ThemeTypography:
    """Data class for theme typography"""
    heading_font: str
    body_font: str
    font_style: str

@dataclass
class PresentationTheme:
    """Complete presentation theme definition"""
    name: str
    description: str
    colors: ThemeColors
    typography: ThemeTypography
    layout_style: str
    visual_elements: List[str]

class ThemeService:
    """Service for managing and applying presentation themes"""
    
    def __init__(self):
        self.predefined_themes = self._initialize_predefined_themes()
    
    def _initialize_predefined_themes(self) -> Dict[str, PresentationTheme]:
        """Initialize predefined theme library"""
        themes = {}
        
        # Professional Business Theme
        themes['professional_blue'] = PresentationTheme(
            name='Professional Blue',
            description='Clean, corporate theme perfect for business presentations',
            colors=ThemeColors(
                primary='#1e40af',
                secondary='#3b82f6',
                accent='#ef4444',
                text='#374151',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Calibri',
                body_font='Calibri',
                font_style='modern'
            ),
            layout_style='corporate',
            visual_elements=['clean lines', 'minimal graphics', 'professional icons']
        )
        
        # Creative Theme
        themes['creative_purple'] = PresentationTheme(
            name='Creative Purple',
            description='Vibrant, creative theme for innovative presentations',
            colors=ThemeColors(
                primary='#7c3aed',
                secondary='#a855f7',
                accent='#f59e0b',
                text='#374151',
                background='#fafafa'
            ),
            typography=ThemeTypography(
                heading_font='Segoe UI',
                body_font='Segoe UI',
                font_style='creative'
            ),
            layout_style='creative',
            visual_elements=['gradients', 'creative shapes', 'dynamic layouts']
        )
        
        # Academic Theme
        themes['academic_navy'] = PresentationTheme(
            name='Academic Navy',
            description='Scholarly theme suitable for academic and research presentations',
            colors=ThemeColors(
                primary='#1e3a8a',
                secondary='#3730a3',
                accent='#dc2626',
                text='#1f2937',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Times New Roman',
                body_font='Times New Roman',
                font_style='classic'
            ),
            layout_style='academic',
            visual_elements=['traditional layouts', 'scholarly elements', 'data visualization']
        )
        
        # Technology Theme
        themes['tech_green'] = PresentationTheme(
            name='Tech Green',
            description='Modern technology theme with clean, futuristic design',
            colors=ThemeColors(
                primary='#059669',
                secondary='#10b981',
                accent='#f97316',
                text='#111827',
                background='#f9fafb'
            ),
            typography=ThemeTypography(
                heading_font='Arial',
                body_font='Arial',
                font_style='modern'
            ),
            layout_style='minimalist',
            visual_elements=['tech icons', 'clean geometry', 'modern graphics']
        )
        
        # Healthcare Theme
        themes['healthcare_teal'] = PresentationTheme(
            name='Healthcare Teal',
            description='Professional healthcare theme with calming colors',
            colors=ThemeColors(
                primary='#0d9488',
                secondary='#14b8a6',
                accent='#e11d48',
                text='#374151',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Calibri',
                body_font='Calibri',
                font_style='modern'
            ),
            layout_style='corporate',
            visual_elements=['medical icons', 'clean design', 'trust elements']
        )
        
        # Finance Theme
        themes['finance_gold'] = PresentationTheme(
            name='Finance Gold',
            description='Sophisticated financial theme with premium feel',
            colors=ThemeColors(
                primary='#1f2937',
                secondary='#374151',
                accent='#f59e0b',
                text='#111827',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Georgia',
                body_font='Calibri',
                font_style='classic'
            ),
            layout_style='corporate',
            visual_elements=['premium design', 'financial charts', 'elegant layouts']
        )
        
        return themes
    
    def get_theme_by_name(self, theme_name: str) -> Optional[PresentationTheme]:
        """Get a predefined theme by name"""
        return self.predefined_themes.get(theme_name.lower().replace(' ', '_'))
    
    def create_theme_from_gemini_data(self, gemini_theme_data: Dict) -> PresentationTheme:
        """Create a PresentationTheme from Gemini AI recommendation"""
        try:
            theme_info = gemini_theme_data.get('recommended_theme', {})
            
            # Extract color scheme
            color_data = theme_info.get('color_scheme', {})
            colors = ThemeColors(
                primary=color_data.get('primary', '#1e40af'),
                secondary=color_data.get('secondary', '#3b82f6'),
                accent=color_data.get('accent', '#ef4444'),
                text=color_data.get('text', '#374151'),
                background=color_data.get('background', '#ffffff')
            )
            
            # Extract typography
            typo_data = theme_info.get('typography', {})
            typography = ThemeTypography(
                heading_font=typo_data.get('heading_font', 'Calibri'),
                body_font=typo_data.get('body_font', 'Calibri'),
                font_style=typo_data.get('font_style', 'modern')
            )
            
            # Create theme
            theme = PresentationTheme(
                name=theme_info.get('name', 'Custom Theme'),
                description=theme_info.get('description', 'AI-generated custom theme'),
                colors=colors,
                typography=typography,
                layout_style=theme_info.get('layout_style', 'corporate'),
                visual_elements=theme_info.get('visual_elements', [])
            )
            
            return theme
            
        except Exception as e:
            logging.error(f"Error creating theme from Gemini data: {e}")
            # Return default theme as fallback
            return self.predefined_themes['professional_blue']
    
    def get_theme_recommendations(self, topic: str, tone: str, industry: str = "") -> List[str]:
        """Get theme recommendations based on content analysis"""
        recommendations = []
        
        # Topic-based recommendations
        topic_lower = topic.lower()
        
        if any(word in topic_lower for word in ['business', 'corporate', 'strategy', 'management']):
            recommendations.append('professional_blue')
        
        if any(word in topic_lower for word in ['creative', 'design', 'innovation', 'art']):
            recommendations.append('creative_purple')
        
        if any(word in topic_lower for word in ['research', 'academic', 'study', 'analysis']):
            recommendations.append('academic_navy')
        
        if any(word in topic_lower for word in ['technology', 'tech', 'software', 'digital']):
            recommendations.append('tech_green')
        
        if any(word in topic_lower for word in ['health', 'medical', 'healthcare', 'medicine']):
            recommendations.append('healthcare_teal')
        
        if any(word in topic_lower for word in ['finance', 'financial', 'investment', 'banking']):
            recommendations.append('finance_gold')
        
        # Tone-based recommendations
        if tone == 'professional':
            if 'professional_blue' not in recommendations:
                recommendations.append('professional_blue')
        elif tone == 'creative':
            if 'creative_purple' not in recommendations:
                recommendations.append('creative_purple')
        elif tone == 'academic':
            if 'academic_navy' not in recommendations:
                recommendations.append('academic_navy')
        
        # Default fallback
        if not recommendations:
            recommendations.append('professional_blue')
        
        return recommendations[:3]  # Return top 3 recommendations
    
    def get_all_themes(self) -> Dict[str, Dict]:
        """Get all available themes for frontend display"""
        themes_data = {}
        
        for theme_id, theme in self.predefined_themes.items():
            themes_data[theme_id] = {
                'name': theme.name,
                'description': theme.description,
                'colors': {
                    'primary': theme.colors.primary,
                    'secondary': theme.colors.secondary,
                    'accent': theme.colors.accent,
                    'text': theme.colors.text,
                    'background': theme.colors.background
                },
                'typography': {
                    'heading_font': theme.typography.heading_font,
                    'body_font': theme.typography.body_font,
                    'font_style': theme.typography.font_style
                },
                'layout_style': theme.layout_style,
                'visual_elements': theme.visual_elements
            }
        
        return themes_data
    
    def validate_theme_colors(self, colors: ThemeColors) -> bool:
        """Validate that theme colors are properly formatted"""
        try:
            # Test conversion to RGB
            colors.to_rgb(colors.primary)
            colors.to_rgb(colors.secondary)
            colors.to_rgb(colors.accent)
            colors.to_rgb(colors.text)
            colors.to_rgb(colors.background)
            return True
        except Exception:
            return False
