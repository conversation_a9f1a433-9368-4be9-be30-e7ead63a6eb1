from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE
from pptx.enum.dml import MSO_THEME_COLOR
import os
import logging
from .theme_service import ThemeService, PresentationTheme

class PowerPointGenerator:
    """Generate PowerPoint presentations with dynamic themes"""

    def __init__(self):
        self.theme_service = ThemeService()

        # Legacy color schemes for backward compatibility
        self.legacy_color_schemes = {
            'professional': {
                'primary': RGBColor(31, 73, 125),
                'secondary': RGBColor(79, 129, 189),
                'accent': RGBColor(192, 80, 77),
                'text': RGBColor(64, 64, 64),
                'background': RGBColor(255, 255, 255)
            },
            'casual': {
                'primary': RGBColor(46, 125, 50),
                'secondary': RGBColor(102, 187, 106),
                'accent': RGBColor(255, 152, 0),
                'text': RGBColor(33, 33, 33),
                'background': RGBColor(250, 250, 250)
            },
            'academic': {
                'primary': RGBColor(26, 35, 126),
                'secondary': RGBColor(63, 81, 181),
                'accent': RGBColor(121, 85, 72),
                'text': RGBColor(33, 33, 33),
                'background': RGBColor(255, 255, 255)
            },
            'creative': {
                'primary': RGBColor(142, 36, 170),
                'secondary': RGBColor(186, 104, 200),
                'accent': RGBColor(255, 193, 7),
                'text': RGBColor(66, 66, 66),
                'background': RGBColor(248, 248, 248)
            }
        }
    
    def create_presentation(self, slide_content, output_path, tone='professional',
                          theme_name=None, custom_theme=None):
        """Create a PowerPoint presentation with dynamic themes"""
        try:
            # Create presentation
            prs = Presentation()

            # Set slide dimensions (16:9 aspect ratio)
            prs.slide_width = Inches(13.33)
            prs.slide_height = Inches(7.5)

            # Determine theme to use
            theme = self._get_presentation_theme(tone, theme_name, custom_theme)

            # Create title slide
            self.create_title_slide(prs, slide_content, theme)

            # Create content slides
            for slide_data in slide_content[1:]:  # Skip first slide as it's usually intro
                self.create_content_slide(prs, slide_data, theme)

            # Save presentation
            prs.save(output_path)
            logging.info(f"Presentation created successfully: {output_path}")
            return output_path

        except Exception as e:
            logging.error(f"Error creating presentation: {e}")
            raise

    def _get_presentation_theme(self, tone, theme_name=None, custom_theme=None):
        """Get the appropriate theme for the presentation"""
        if custom_theme:
            # Use custom theme from Gemini
            return custom_theme
        elif theme_name:
            # Use predefined theme by name
            theme = self.theme_service.get_theme_by_name(theme_name)
            if theme:
                return theme

        # Fallback to legacy color scheme
        return self._create_legacy_theme(tone)

    def _create_legacy_theme(self, tone):
        """Create a theme object from legacy color schemes"""
        colors = self.legacy_color_schemes.get(tone, self.legacy_color_schemes['professional'])

        # Create a simple theme object for backward compatibility
        class LegacyTheme:
            def __init__(self, colors):
                self.colors = colors
                self.name = f"Legacy {tone.title()}"
                self.typography = type('obj', (object,), {
                    'heading_font': 'Calibri',
                    'body_font': 'Calibri'
                })()

        return LegacyTheme(colors)
    
    def create_title_slide(self, prs, slide_content, theme):
        """Create the title slide with dynamic theme"""
        # Use blank layout
        slide_layout = prs.slide_layouts[6]  # Blank layout
        slide = prs.slides.add_slide(slide_layout)

        # Get colors from theme
        colors = self._get_theme_colors(theme)

        # Set background color
        background = slide.background
        fill = background.fill
        fill.solid()
        fill.fore_color.rgb = colors['background']
        
        # Add title
        title_box = slide.shapes.add_textbox(
            Inches(1), Inches(2), Inches(11.33), Inches(2)
        )
        title_frame = title_box.text_frame
        title_frame.text = slide_content[0]['title'] if slide_content else "Presentation"
        
        # Style title with theme typography
        title_paragraph = title_frame.paragraphs[0]
        title_paragraph.alignment = PP_ALIGN.CENTER
        title_run = title_paragraph.runs[0]
        title_run.font.name = self._get_theme_font(theme, 'heading')
        title_run.font.size = Pt(44)
        title_run.font.bold = True
        title_run.font.color.rgb = colors['primary']
        
        # Add subtitle if available
        if slide_content and slide_content[0].get('content'):
            subtitle_text = slide_content[0]['content'][0] if slide_content[0]['content'] else ""
            subtitle_box = slide.shapes.add_textbox(
                Inches(2), Inches(4.5), Inches(9.33), Inches(1)
            )
            subtitle_frame = subtitle_box.text_frame
            subtitle_frame.text = subtitle_text
            
            subtitle_paragraph = subtitle_frame.paragraphs[0]
            subtitle_paragraph.alignment = PP_ALIGN.CENTER
            subtitle_run = subtitle_paragraph.runs[0]
            subtitle_run.font.name = self._get_theme_font(theme, 'body')
            subtitle_run.font.size = Pt(24)
            subtitle_run.font.color.rgb = colors['secondary']
        
        # Add decorative shape
        shape = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE,
            Inches(0), Inches(6.8), Inches(13.33), Inches(0.7)
        )
        shape.fill.solid()
        shape.fill.fore_color.rgb = colors['primary']
        shape.line.fill.background()
    
    def create_content_slide(self, prs, slide_data, theme):
        """Create a content slide with title and bullet points using dynamic theme"""
        # Use blank layout
        slide_layout = prs.slide_layouts[6]  # Blank layout
        slide = prs.slides.add_slide(slide_layout)

        # Get colors from theme
        colors = self._get_theme_colors(theme)

        # Set background color
        background = slide.background
        fill = background.fill
        fill.solid()
        fill.fore_color.rgb = colors['background']
        
        # Add header bar
        header_shape = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE,
            Inches(0), Inches(0), Inches(13.33), Inches(1.2)
        )
        header_shape.fill.solid()
        header_shape.fill.fore_color.rgb = colors['primary']
        header_shape.line.fill.background()
        
        # Add title
        title_box = slide.shapes.add_textbox(
            Inches(0.5), Inches(0.2), Inches(12.33), Inches(0.8)
        )
        title_frame = title_box.text_frame
        title_frame.text = slide_data['title']
        title_frame.margin_left = Inches(0.2)
        title_frame.margin_top = Inches(0.1)
        title_frame.vertical_anchor = MSO_ANCHOR.MIDDLE
        
        # Style title with theme typography
        title_paragraph = title_frame.paragraphs[0]
        title_paragraph.alignment = PP_ALIGN.LEFT
        title_run = title_paragraph.runs[0]
        title_run.font.name = self._get_theme_font(theme, 'heading')
        title_run.font.size = Pt(32)
        title_run.font.bold = True
        title_run.font.color.rgb = RGBColor(255, 255, 255)  # White text
        
        # Add content area
        content_box = slide.shapes.add_textbox(
            Inches(1), Inches(1.8), Inches(11.33), Inches(5)
        )
        content_frame = content_box.text_frame
        content_frame.margin_left = Inches(0.2)
        content_frame.margin_top = Inches(0.2)
        
        # Add bullet points
        if slide_data.get('content'):
            for i, bullet_point in enumerate(slide_data['content']):
                if i == 0:
                    # First paragraph
                    p = content_frame.paragraphs[0]
                else:
                    # Add new paragraph
                    p = content_frame.add_paragraph()
                
                p.text = bullet_point
                p.level = 0
                
                # Style bullet point with theme typography
                run = p.runs[0]
                run.font.name = self._get_theme_font(theme, 'body')
                run.font.size = Pt(20)
                run.font.color.rgb = colors['text']
                
                # Add spacing between bullet points
                p.space_after = Pt(12)
        
        # Add accent line at bottom
        accent_shape = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE,
            Inches(1), Inches(6.8), Inches(11.33), Inches(0.1)
        )
        accent_shape.fill.solid()
        accent_shape.fill.fore_color.rgb = colors['accent']
        accent_shape.line.fill.background()
        
        # Add slide number
        slide_num_box = slide.shapes.add_textbox(
            Inches(11.5), Inches(7), Inches(1.5), Inches(0.4)
        )
        slide_num_frame = slide_num_box.text_frame
        slide_num_frame.text = str(slide_data.get('slide_number', ''))
        
        slide_num_paragraph = slide_num_frame.paragraphs[0]
        slide_num_paragraph.alignment = PP_ALIGN.RIGHT
        slide_num_run = slide_num_paragraph.runs[0]
        slide_num_run.font.name = self._get_theme_font(theme, 'body')
        slide_num_run.font.size = Pt(12)
        slide_num_run.font.color.rgb = colors['secondary']

    def _get_theme_colors(self, theme):
        """Extract colors from theme object"""
        if hasattr(theme, 'colors') and hasattr(theme.colors, 'primary'):
            # New theme system
            return {
                'primary': theme.colors.to_rgb(theme.colors.primary),
                'secondary': theme.colors.to_rgb(theme.colors.secondary),
                'accent': theme.colors.to_rgb(theme.colors.accent),
                'text': theme.colors.to_rgb(theme.colors.text),
                'background': theme.colors.to_rgb(theme.colors.background)
            }
        else:
            # Legacy theme system
            return theme.colors

    def _get_theme_font(self, theme, font_type='body'):
        """Get font from theme object"""
        if hasattr(theme, 'typography'):
            if font_type == 'heading':
                return getattr(theme.typography, 'heading_font', 'Calibri')
            else:
                return getattr(theme.typography, 'body_font', 'Calibri')
        else:
            return 'Calibri'

    def create_basic_presentation(self, slide_content, output_path, topic):
        """Create a basic presentation with minimal styling"""
        try:
            prs = Presentation()

            # Create title slide
            slide = prs.slides[0]
            title = slide.shapes.title
            subtitle = slide.placeholders[1]

            title.text = topic
            subtitle.text = "Professional Presentation"

            # Create content slides
            for slide_data in slide_content:
                slide = prs.slides.add_slide(prs.slide_layouts[1])

                # Add title
                title = slide.shapes.title
                title.text = slide_data.get('title', 'Slide Title')

                # Add content
                content_placeholder = slide.placeholders[1]
                content = slide_data.get('content', [])
                if content:
                    content_placeholder.text = '\n'.join([f"• {item}" for item in content])

            prs.save(output_path)
            print(f"Basic presentation saved to: {output_path}")

        except Exception as e:
            print(f"Basic presentation creation failed: {e}")
            raise

    def create_minimal_presentation(self, topic, output_path):
        """Create minimal presentation with just topic"""
        try:
            prs = Presentation()

            # Title slide
            slide = prs.slides[0]
            title = slide.shapes.title
            subtitle = slide.placeholders[1]

            title.text = topic
            subtitle.text = "Professional Presentation"

            # Content slide
            slide = prs.slides.add_slide(prs.slide_layouts[1])
            title = slide.shapes.title
            content = slide.placeholders[1]

            title.text = f"{topic}: Key Points"
            content.text = "• Overview and introduction\n• Main concepts\n• Benefits and advantages\n• Next steps"

            prs.save(output_path)
            print(f"Minimal presentation saved to: {output_path}")

        except Exception as e:
            print(f"Minimal presentation creation failed: {e}")
            raise

    def create_emergency_presentation(self, topic, output_path):
        """Create emergency fallback presentation"""
        try:
            prs = Presentation()

            # Ensure we have at least one slide
            if len(prs.slides) == 0:
                slide = prs.slides.add_slide(prs.slide_layouts[0])
            else:
                slide = prs.slides[0]

            # Safely set title and subtitle
            try:
                title = slide.shapes.title
                if title:
                    title.text = str(topic) if topic else "Presentation"
            except:
                pass

            try:
                if len(slide.placeholders) > 1:
                    subtitle = slide.placeholders[1]
                    subtitle.text = "Generated by SlideForge AI"
            except:
                pass

            # Add a basic content slide
            try:
                content_slide = prs.slides.add_slide(prs.slide_layouts[1])
                content_title = content_slide.shapes.title
                content_title.text = "Key Points"

                content_placeholder = content_slide.placeholders[1]
                content_placeholder.text = "• Overview\n• Main concepts\n• Benefits\n• Next steps"
            except:
                pass

            prs.save(output_path)
            print(f"Emergency presentation saved to: {output_path}")

        except Exception as e:
            print(f"Emergency presentation creation failed: {e}")
            # Create absolute minimal file
            try:
                from pptx import Presentation
                minimal_prs = Presentation()
                minimal_prs.save(output_path)
            except:
                # Last resort - create empty file
                with open(output_path, 'wb') as f:
                    f.write(b'')
            raise
