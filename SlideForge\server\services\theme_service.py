import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from pptx.dml.color import RGBColor

@dataclass
class ThemeColors:
    """Data class for theme color schemes"""
    primary: str
    secondary: str
    accent: str
    text: str
    background: str
    
    def to_rgb(self, color_hex: str) -> RGBColor:
        """Convert hex color to RGBColor object"""
        hex_color = color_hex.lstrip('#')
        return RGBColor(
            int(hex_color[0:2], 16),
            int(hex_color[2:4], 16),
            int(hex_color[4:6], 16)
        )

@dataclass
class ThemeTypography:
    """Data class for theme typography"""
    heading_font: str
    body_font: str
    font_style: str

@dataclass
class PresentationTheme:
    """Complete presentation theme definition"""
    name: str
    description: str
    colors: ThemeColors
    typography: ThemeTypography
    layout_style: str
    visual_elements: List[str]

class ThemeService:
    """Service for managing and applying presentation themes"""
    
    def __init__(self):
        self.predefined_themes = self._initialize_predefined_themes()
    
    def _initialize_predefined_themes(self) -> Dict[str, PresentationTheme]:
        """Initialize predefined theme library"""
        themes = {}
        
        # Professional Business Theme
        themes['professional_blue'] = PresentationTheme(
            name='Professional Blue',
            description='Clean, corporate theme perfect for business presentations',
            colors=ThemeColors(
                primary='#1e40af',
                secondary='#3b82f6',
                accent='#ef4444',
                text='#374151',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Calibri',
                body_font='Calibri',
                font_style='modern'
            ),
            layout_style='corporate',
            visual_elements=['clean lines', 'minimal graphics', 'professional icons']
        )
        
        # Creative Theme
        themes['creative_purple'] = PresentationTheme(
            name='Creative Purple',
            description='Vibrant, creative theme for innovative presentations',
            colors=ThemeColors(
                primary='#7c3aed',
                secondary='#a855f7',
                accent='#f59e0b',
                text='#374151',
                background='#fafafa'
            ),
            typography=ThemeTypography(
                heading_font='Segoe UI',
                body_font='Segoe UI',
                font_style='creative'
            ),
            layout_style='creative',
            visual_elements=['gradients', 'creative shapes', 'dynamic layouts']
        )
        
        # Academic Theme
        themes['academic_navy'] = PresentationTheme(
            name='Academic Navy',
            description='Scholarly theme suitable for academic and research presentations',
            colors=ThemeColors(
                primary='#1e3a8a',
                secondary='#3730a3',
                accent='#dc2626',
                text='#1f2937',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Times New Roman',
                body_font='Times New Roman',
                font_style='classic'
            ),
            layout_style='academic',
            visual_elements=['traditional layouts', 'scholarly elements', 'data visualization']
        )
        
        # Technology Theme
        themes['tech_green'] = PresentationTheme(
            name='Tech Green',
            description='Modern technology theme with clean, futuristic design',
            colors=ThemeColors(
                primary='#059669',
                secondary='#10b981',
                accent='#f97316',
                text='#111827',
                background='#f9fafb'
            ),
            typography=ThemeTypography(
                heading_font='Arial',
                body_font='Arial',
                font_style='modern'
            ),
            layout_style='minimalist',
            visual_elements=['tech icons', 'clean geometry', 'modern graphics']
        )
        
        # Healthcare Theme
        themes['healthcare_teal'] = PresentationTheme(
            name='Healthcare Teal',
            description='Professional healthcare theme with calming colors',
            colors=ThemeColors(
                primary='#0d9488',
                secondary='#14b8a6',
                accent='#e11d48',
                text='#374151',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Calibri',
                body_font='Calibri',
                font_style='modern'
            ),
            layout_style='corporate',
            visual_elements=['medical icons', 'clean design', 'trust elements']
        )

        # Premium Executive Theme
        themes['executive_platinum'] = PresentationTheme(
            name='Executive Platinum',
            description='Sophisticated executive theme with premium metallic accents',
            colors=ThemeColors(
                primary='#1a1a1a',
                secondary='#4a5568',
                accent='#c6a96b',
                text='#2d3748',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Georgia',
                body_font='Calibri',
                font_style='classic'
            ),
            layout_style='executive',
            visual_elements=['premium design', 'metallic accents', 'executive layouts']
        )

        # Modern Gradient Theme
        themes['modern_gradient'] = PresentationTheme(
            name='Modern Gradient',
            description='Contemporary theme with sophisticated gradient elements',
            colors=ThemeColors(
                primary='#667eea',
                secondary='#764ba2',
                accent='#f093fb',
                text='#2d3748',
                background='#f8fafc'
            ),
            typography=ThemeTypography(
                heading_font='Segoe UI',
                body_font='Segoe UI',
                font_style='modern'
            ),
            layout_style='modern',
            visual_elements=['gradients', 'modern shapes', 'dynamic layouts']
        )

        # Elegant Dark Theme
        themes['elegant_dark'] = PresentationTheme(
            name='Elegant Dark',
            description='Sophisticated dark theme for premium presentations',
            colors=ThemeColors(
                primary='#1a202c',
                secondary='#2d3748',
                accent='#ed8936',
                text='#e2e8f0',
                background='#2d3748'
            ),
            typography=ThemeTypography(
                heading_font='Calibri',
                body_font='Calibri',
                font_style='modern'
            ),
            layout_style='premium',
            visual_elements=['dark elegance', 'premium feel', 'sophisticated design']
        )

        # Vibrant Energy Theme
        themes['vibrant_energy'] = PresentationTheme(
            name='Vibrant Energy',
            description='Dynamic theme with energetic colors for impactful presentations',
            colors=ThemeColors(
                primary='#e53e3e',
                secondary='#fd9801',
                accent='#38b2ac',
                text='#2d3748',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Arial',
                body_font='Arial',
                font_style='modern'
            ),
            layout_style='dynamic',
            visual_elements=['bold colors', 'energetic design', 'impact layouts']
        )

        # Minimalist Zen Theme
        themes['minimalist_zen'] = PresentationTheme(
            name='Minimalist Zen',
            description='Clean, minimal theme focusing on content clarity',
            colors=ThemeColors(
                primary='#4a5568',
                secondary='#718096',
                accent='#38b2ac',
                text='#2d3748',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Helvetica',
                body_font='Helvetica',
                font_style='minimal'
            ),
            layout_style='minimal',
            visual_elements=['clean lines', 'white space', 'minimal design']
        )
        
        # Finance Theme
        themes['finance_gold'] = PresentationTheme(
            name='Finance Gold',
            description='Sophisticated financial theme with premium feel',
            colors=ThemeColors(
                primary='#1f2937',
                secondary='#374151',
                accent='#f59e0b',
                text='#111827',
                background='#ffffff'
            ),
            typography=ThemeTypography(
                heading_font='Georgia',
                body_font='Calibri',
                font_style='classic'
            ),
            layout_style='corporate',
            visual_elements=['premium design', 'financial charts', 'elegant layouts']
        )
        
        return themes
    
    def get_theme_by_name(self, theme_name: str) -> Optional[PresentationTheme]:
        """Get a predefined theme by name"""
        return self.predefined_themes.get(theme_name.lower().replace(' ', '_'))
    
    def create_theme_from_gemini_data(self, gemini_theme_data: Dict) -> PresentationTheme:
        """Create a PresentationTheme from Gemini AI recommendation"""
        try:
            theme_info = gemini_theme_data.get('recommended_theme', {})
            
            # Extract color scheme
            color_data = theme_info.get('color_scheme', {})
            colors = ThemeColors(
                primary=color_data.get('primary', '#1e40af'),
                secondary=color_data.get('secondary', '#3b82f6'),
                accent=color_data.get('accent', '#ef4444'),
                text=color_data.get('text', '#374151'),
                background=color_data.get('background', '#ffffff')
            )
            
            # Extract typography
            typo_data = theme_info.get('typography', {})
            typography = ThemeTypography(
                heading_font=typo_data.get('heading_font', 'Calibri'),
                body_font=typo_data.get('body_font', 'Calibri'),
                font_style=typo_data.get('font_style', 'modern')
            )
            
            # Create theme
            theme = PresentationTheme(
                name=theme_info.get('name', 'Custom Theme'),
                description=theme_info.get('description', 'AI-generated custom theme'),
                colors=colors,
                typography=typography,
                layout_style=theme_info.get('layout_style', 'corporate'),
                visual_elements=theme_info.get('visual_elements', [])
            )
            
            return theme
            
        except Exception as e:
            logging.error(f"Error creating theme from Gemini data: {e}")
            # Return default theme as fallback
            return self.predefined_themes['professional_blue']
    
    def get_theme_recommendations(self, topic: str, tone: str, mood: str = 'modern', industry: str = "") -> List[str]:
        """Get theme recommendations based on content analysis"""
        recommendations = []
        
        # Topic-based recommendations
        topic_lower = topic.lower()
        
        if any(word in topic_lower for word in ['business', 'corporate', 'strategy', 'management']):
            recommendations.extend(['professional_blue', 'executive_platinum'])

        if any(word in topic_lower for word in ['creative', 'design', 'innovation', 'art']):
            recommendations.extend(['creative_purple', 'modern_gradient', 'vibrant_energy'])

        if any(word in topic_lower for word in ['research', 'academic', 'study', 'analysis']):
            recommendations.extend(['academic_navy', 'minimalist_zen'])

        if any(word in topic_lower for word in ['technology', 'tech', 'software', 'digital']):
            recommendations.extend(['tech_green', 'modern_gradient', 'elegant_dark'])

        if any(word in topic_lower for word in ['health', 'medical', 'healthcare', 'medicine']):
            recommendations.extend(['healthcare_teal', 'minimalist_zen'])

        if any(word in topic_lower for word in ['finance', 'financial', 'investment', 'banking']):
            recommendations.extend(['finance_gold', 'executive_platinum'])

        if any(word in topic_lower for word in ['executive', 'leadership', 'board', 'ceo']):
            recommendations.extend(['executive_platinum', 'elegant_dark'])

        if any(word in topic_lower for word in ['startup', 'innovation', 'disrupt', 'modern']):
            recommendations.extend(['modern_gradient', 'vibrant_energy'])

        if any(word in topic_lower for word in ['minimal', 'simple', 'clean', 'focus']):
            recommendations.extend(['minimalist_zen', 'professional_blue'])
        
        # Tone-based recommendations
        if tone == 'professional':
            if 'professional_blue' not in recommendations:
                recommendations.append('professional_blue')
        elif tone == 'creative':
            if 'creative_purple' not in recommendations:
                recommendations.append('creative_purple')
        elif tone == 'academic':
            if 'academic_navy' not in recommendations:
                recommendations.append('academic_navy')
        
        # Default fallback
        if not recommendations:
            recommendations.append('professional_blue')
        
        return recommendations[:3]  # Return top 3 recommendations
    
    def get_all_themes(self) -> Dict[str, Dict]:
        """Get all available themes for frontend display"""
        themes_data = {}
        
        for theme_id, theme in self.predefined_themes.items():
            themes_data[theme_id] = {
                'name': theme.name,
                'description': theme.description,
                'colors': {
                    'primary': theme.colors.primary,
                    'secondary': theme.colors.secondary,
                    'accent': theme.colors.accent,
                    'text': theme.colors.text,
                    'background': theme.colors.background
                },
                'typography': {
                    'heading_font': theme.typography.heading_font,
                    'body_font': theme.typography.body_font,
                    'font_style': theme.typography.font_style
                },
                'layout_style': theme.layout_style,
                'visual_elements': theme.visual_elements
            }
        
        return themes_data
    
    def validate_theme_colors(self, colors: ThemeColors) -> bool:
        """Validate that theme colors are properly formatted"""
        try:
            # Test conversion to RGB
            colors.to_rgb(colors.primary)
            colors.to_rgb(colors.secondary)
            colors.to_rgb(colors.accent)
            colors.to_rgb(colors.text)
            colors.to_rgb(colors.background)
            return True
        except Exception:
            return False

    def get_intelligent_theme_for_topic(self, topic: str, tone: str):
        """Get an intelligent theme selection based on topic analysis"""
        try:
            topic_lower = topic.lower()

            # Create a custom theme based on topic
            if any(word in topic_lower for word in ['finance', 'financial', 'money', 'investment', 'banking']):
                return self._create_custom_theme(
                    name="Financial Excellence",
                    description="Professional financial theme with trust-building colors",
                    primary="#1a365d", secondary="#2d5a87", accent="#f6ad55",
                    background="#ffffff", text="#2d3748"
                )
            elif any(word in topic_lower for word in ['technology', 'tech', 'ai', 'digital', 'software']):
                return self._create_custom_theme(
                    name="Tech Innovation",
                    description="Modern technology theme with cutting-edge design",
                    primary="#1a202c", secondary="#2d3748", accent="#38b2ac",
                    background="#f7fafc", text="#1a202c"
                )
            elif any(word in topic_lower for word in ['health', 'medical', 'healthcare', 'wellness']):
                return self._create_custom_theme(
                    name="Healthcare Professional",
                    description="Clean healthcare theme promoting trust and care",
                    primary="#0d9488", secondary="#14b8a6", accent="#f59e0b",
                    background="#ffffff", text="#374151"
                )
            elif any(word in topic_lower for word in ['education', 'learning', 'academic', 'research']):
                return self._create_custom_theme(
                    name="Academic Excellence",
                    description="Scholarly theme for educational presentations",
                    primary="#1e3a8a", secondary="#3730a3", accent="#dc2626",
                    background="#ffffff", text="#1f2937"
                )
            else:
                # Default professional theme
                return self._create_custom_theme(
                    name="Professional Excellence",
                    description="Versatile professional theme for any presentation",
                    primary="#1e40af", secondary="#3b82f6", accent="#ef4444",
                    background="#ffffff", text="#374151"
                )
        except Exception as e:
            print(f"Error creating intelligent theme: {e}")
            # Return a safe default
            return self._create_custom_theme(
                name="Classic Professional",
                description="Reliable professional theme",
                primary="#1e40af", secondary="#3b82f6", accent="#ef4444",
                background="#ffffff", text="#374151"
            )

    def _create_custom_theme(self, name, description, primary, secondary, accent, background, text):
        """Create a custom theme object"""
        from dataclasses import dataclass

        @dataclass
        class CustomTheme:
            name: str
            description: str
            colors: object

        @dataclass
        class ThemeColors:
            primary: str
            secondary: str
            accent: str
            background: str
            text: str

        return CustomTheme(
            name=name,
            description=description,
            colors=ThemeColors(
                primary=primary,
                secondary=secondary,
                accent=accent,
                background=background,
                text=text
            )
        )
