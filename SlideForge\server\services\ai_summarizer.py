import re
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity
import nltk
from textblob import TextBlob
from collections import Counter
import string

class AISummarizer:
    """Custom AI text summarizer using rule-based and ML approaches"""
    
    def __init__(self):
        self.download_nltk_data()
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        
        # Predefined knowledge base for different topics
        self.topic_templates = {
            'business': {
                'structure': ['Introduction', 'Problem Statement', 'Solution', 'Market Analysis', 'Implementation', 'Conclusion'],
                'keywords': ['strategy', 'market', 'revenue', 'growth', 'competition', 'analysis', 'profit', 'investment']
            },
            'technology': {
                'structure': ['Overview', 'Technical Requirements', 'Architecture', 'Implementation', 'Benefits', 'Future Scope'],
                'keywords': ['system', 'technology', 'development', 'architecture', 'implementation', 'performance', 'scalability']
            },
            'education': {
                'structure': ['Introduction', 'Learning Objectives', 'Key Concepts', 'Examples', 'Practice', 'Summary'],
                'keywords': ['learning', 'education', 'knowledge', 'skills', 'understanding', 'practice', 'development']
            },
            'research': {
                'structure': ['Background', 'Methodology', 'Findings', 'Analysis', 'Implications', 'Conclusion'],
                'keywords': ['research', 'study', 'analysis', 'findings', 'methodology', 'results', 'conclusion']
            }
        }
        
        self.tone_adjustments = {
            'professional': {'formality': 'high', 'complexity': 'medium'},
            'casual': {'formality': 'low', 'complexity': 'low'},
            'academic': {'formality': 'high', 'complexity': 'high'},
            'creative': {'formality': 'medium', 'complexity': 'medium'}
        }
    
    def download_nltk_data(self):
        """Download required NLTK data"""
        try:
            import nltk
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('averaged_perceptron_tagger', quiet=True)
        except:
            pass
    
    def preprocess_text(self, text):
        """Clean and preprocess text"""
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\,\!\?\-]', '', text)
        
        return text
    
    def extract_key_phrases(self, text, max_phrases=10):
        """Extract key phrases using TF-IDF and n-grams"""
        if not text.strip():
            return []
        
        # Preprocess text
        clean_text = self.preprocess_text(text)
        
        try:
            # Use TF-IDF to find important terms
            tfidf_matrix = self.vectorizer.fit_transform([clean_text])
            feature_names = self.vectorizer.get_feature_names_out()
            tfidf_scores = tfidf_matrix.toarray()[0]
            
            # Get top scoring phrases
            phrase_scores = list(zip(feature_names, tfidf_scores))
            phrase_scores.sort(key=lambda x: x[1], reverse=True)
            
            return [phrase for phrase, score in phrase_scores[:max_phrases] if score > 0]
        except:
            # Fallback to simple word frequency
            words = clean_text.lower().split()
            word_freq = Counter(words)
            return [word for word, freq in word_freq.most_common(max_phrases)]
    
    def detect_topic_category(self, topic, outline):
        """Detect the category of the topic for appropriate structuring"""
        combined_text = f"{topic} {outline}".lower()
        
        category_scores = {}
        for category, data in self.topic_templates.items():
            score = sum(1 for keyword in data['keywords'] if keyword in combined_text)
            category_scores[category] = score
        
        # Return category with highest score, default to 'business'
        best_category = max(category_scores, key=category_scores.get)
        return best_category if category_scores[best_category] > 0 else 'business'
    
    def generate_slide_structure(self, topic, outline, category, slide_count=10):
        """Generate slide structure based on topic, category, and desired slide count"""
        base_structure = self.topic_templates[category]['structure']

        # If outline is provided, try to incorporate it
        if outline.strip():
            outline_points = [point.strip() for point in outline.split('\n') if point.strip()]
            if len(outline_points) > 2:
                # Use outline as structure if it has enough points
                structure = ['Introduction'] + outline_points + ['Conclusion']
                # Adjust to match slide count
                return self._adjust_structure_to_count(structure, slide_count)

        # Adjust base structure to match slide count
        return self._adjust_structure_to_count(base_structure, slide_count)

    def _adjust_structure_to_count(self, structure, target_count):
        """Adjust slide structure to match target slide count"""
        current_count = len(structure)

        if current_count == target_count:
            return structure
        elif current_count < target_count:
            # Add more slides
            additional_slides = target_count - current_count
            for i in range(additional_slides):
                structure.insert(-1, f"Key Point {i + 1}")  # Insert before conclusion
            return structure
        else:
            # Reduce slides (keep intro and conclusion, trim middle)
            if target_count < 3:
                return structure[:target_count]
            else:
                # Keep intro, conclusion, and most important middle slides
                intro = structure[:1]
                conclusion = structure[-1:]
                middle_count = target_count - 2
                middle = structure[1:-1][:middle_count]
                return intro + middle + conclusion
    
    def generate_content_for_slide(self, slide_title, topic, key_phrases, tone, category):
        """Generate content for a specific slide"""
        tone_settings = self.tone_adjustments.get(tone, self.tone_adjustments['professional'])
        
        # Base content templates
        content_templates = {
            'introduction': [
                f"Welcome to our presentation on {topic}",
                f"Today we'll explore {topic}",
                f"Understanding {topic}: Key insights and applications"
            ],
            'conclusion': [
                f"Key takeaways from {topic}",
                f"Summary of main points",
                f"Next steps and recommendations"
            ]
        }
        
        # Generate bullet points based on slide title and key phrases
        bullet_points = []
        
        if 'introduction' in slide_title.lower():
            bullet_points = [
                f"Overview of {topic}",
                f"Importance and relevance",
                f"What we'll cover today"
            ]
        elif 'conclusion' in slide_title.lower():
            bullet_points = [
                "Summary of key points",
                "Main insights and findings",
                "Recommendations for next steps"
            ]
        else:
            # Generate content based on key phrases and slide title
            relevant_phrases = [phrase for phrase in key_phrases if len(phrase.split()) <= 3][:4]
            
            if relevant_phrases:
                bullet_points = [
                    f"Understanding {phrase}" for phrase in relevant_phrases[:2]
                ] + [
                    f"Key aspects of {phrase}" for phrase in relevant_phrases[2:4]
                ]
            else:
                bullet_points = [
                    f"Key concepts in {slide_title.lower()}",
                    f"Important considerations",
                    f"Practical applications",
                    f"Best practices and recommendations"
                ]
        
        # Adjust tone
        if tone_settings['formality'] == 'low':
            bullet_points = [point.replace('Understanding', 'Getting to know') 
                           .replace('Key aspects', 'Main things about') for point in bullet_points]
        elif tone_settings['formality'] == 'high':
            bullet_points = [point.replace('Key', 'Critical') 
                           .replace('Important', 'Essential') for point in bullet_points]
        
        return bullet_points[:4]  # Limit to 4 bullet points per slide
    
    def generate_slide_content(self, topic, tone='professional', outline='', slide_count=10):
        """Main method to generate slide content"""
        # Detect topic category
        category = self.detect_topic_category(topic, outline)
        
        # Extract key phrases from topic and outline
        combined_text = f"{topic} {outline}"
        key_phrases = self.extract_key_phrases(combined_text)
        
        # Generate slide structure
        slide_structure = self.generate_slide_structure(topic, outline, category, slide_count)

        # Generate content for each slide
        slides = []
        for i, slide_title in enumerate(slide_structure):
            content = self.generate_content_for_slide(
                slide_title, topic, key_phrases, tone, category
            )

            slides.append({
                'title': slide_title,
                'content': content,
                'slide_number': i + 1
            })

        return slides
