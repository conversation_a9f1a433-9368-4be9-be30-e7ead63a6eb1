from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
import tempfile
import uuid
from datetime import datetime, timedelta
import threading
import time

from services.ai_summarizer import AISummarizer
from services.pptx_generator import PowerPointGenerator

app = Flask(__name__)
CORS(app)

# Initialize services
ai_summarizer = AISummarizer()
pptx_generator = PowerPointGenerator()

# Store for temporary files (in production, use Redis or database)
temp_files = {}

def cleanup_old_files():
    """Clean up temporary files older than 1 hour"""
    while True:
        current_time = datetime.now()
        files_to_remove = []
        
        for file_id, file_info in temp_files.items():
            if current_time - file_info['created'] > timedelta(hours=1):
                try:
                    if os.path.exists(file_info['path']):
                        os.remove(file_info['path'])
                    files_to_remove.append(file_id)
                except Exception as e:
                    print(f"Error removing file {file_info['path']}: {e}")
        
        for file_id in files_to_remove:
            del temp_files[file_id]
        
        time.sleep(3600)  # Check every hour

# Start cleanup thread
cleanup_thread = threading.Thread(target=cleanup_old_files, daemon=True)
cleanup_thread.start()

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'SlideForge AI'})

@app.route('/api/generate-slides', methods=['POST'])
def generate_slides():
    """Generate PowerPoint slides from user input"""
    try:
        data = request.get_json()
        
        # Validate input
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        topic = data.get('topic', '').strip()
        tone = data.get('tone', 'professional').strip()
        outline = data.get('outline', '').strip()
        
        if not topic:
            return jsonify({'error': 'Topic is required'}), 400
        
        # Generate slide content using AI summarizer
        slide_content = ai_summarizer.generate_slide_content(
            topic=topic,
            tone=tone,
            outline=outline
        )
        
        # Generate PowerPoint file
        temp_dir = tempfile.gettempdir()
        file_id = str(uuid.uuid4())
        filename = f"slides_{file_id}.pptx"
        filepath = os.path.join(temp_dir, filename)
        
        pptx_generator.create_presentation(
            slide_content=slide_content,
            output_path=filepath,
            tone=tone
        )
        
        # Store file info for later download
        temp_files[file_id] = {
            'path': filepath,
            'filename': f"{topic.replace(' ', '_')}_slides.pptx",
            'created': datetime.now()
        }
        
        return jsonify({
            'success': True,
            'file_id': file_id,
            'filename': temp_files[file_id]['filename'],
            'slide_count': len(slide_content),
            'preview': [slide['title'] for slide in slide_content[:3]]  # First 3 slide titles
        })
        
    except Exception as e:
        print(f"Error generating slides: {e}")
        return jsonify({'error': 'Failed to generate slides'}), 500

@app.route('/api/download/<file_id>', methods=['GET'])
def download_file(file_id):
    """Download generated PowerPoint file"""
    try:
        if file_id not in temp_files:
            return jsonify({'error': 'File not found or expired'}), 404
        
        file_info = temp_files[file_id]
        
        if not os.path.exists(file_info['path']):
            return jsonify({'error': 'File not found'}), 404
        
        return send_file(
            file_info['path'],
            as_attachment=True,
            download_name=file_info['filename'],
            mimetype='application/vnd.openxmlformats-officedocument.presentationml.presentation'
        )
        
    except Exception as e:
        print(f"Error downloading file: {e}")
        return jsonify({'error': 'Failed to download file'}), 500

@app.route('/api/preview/<file_id>', methods=['GET'])
def preview_slides(file_id):
    """Get preview information about generated slides"""
    try:
        if file_id not in temp_files:
            return jsonify({'error': 'File not found or expired'}), 404
        
        # In a real implementation, you might extract slide previews
        # For now, return basic info
        return jsonify({
            'file_id': file_id,
            'filename': temp_files[file_id]['filename'],
            'created': temp_files[file_id]['created'].isoformat(),
            'status': 'ready'
        })
        
    except Exception as e:
        print(f"Error getting preview: {e}")
        return jsonify({'error': 'Failed to get preview'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
