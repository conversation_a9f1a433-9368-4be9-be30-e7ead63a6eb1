#!/usr/bin/env python3
"""
Project Slidra - AI-Powered Presentation Generator
Flask application for generating professional PowerPoint presentations
"""

import os
import uuid
import tempfile
import json
from datetime import datetime
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS

# Import services
from services.ai_summarizer import AISummarizer
from services.pptx_generator import PowerPointGenerator
from services.theme_service import ThemeService
from services.gemini_service import GeminiService

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Initialize services
ai_summarizer = AISummarizer()
pptx_generator = PowerPointGenerator()
theme_service = ThemeService()
gemini_service = GeminiService()

# Global storage for temporary files
temp_files = {}

def safe_ai_generation(topic, tone, outline, target_audience, slide_count=10, mood='modern', max_retries=3):
    """Safely generate content with multiple fallback mechanisms"""
    import time
    import random
    
    for attempt in range(max_retries):
        try:
            # Try enhanced AI first
            if gemini_service.is_available:
                try:
                    gemini_content = gemini_service.generate_enhanced_content(
                        topic=topic,
                        tone=tone,
                        outline=outline,
                        target_audience=target_audience,
                        slide_count=slide_count,
                        mood=mood
                    )
                    return gemini_content.get('slides', []), True
                except Exception as e:
                    print(f"Enhanced AI attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        # Exponential backoff
                        time.sleep(2 ** attempt + random.uniform(0, 1))
                        continue
            
            # Fallback to custom AI
            slide_content = ai_summarizer.generate_slide_content(
                topic=topic,
                tone=tone,
                outline=outline,
                slide_count=slide_count
            )
            return slide_content, False
            
        except Exception as e:
            print(f"AI generation attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt + random.uniform(0, 1))
                continue
    
    # Ultimate fallback - generate basic content
    return generate_basic_fallback_content(topic, tone, slide_count), False

def generate_basic_fallback_content(topic, tone, slide_count=10):
    """Generate basic slide content as ultimate fallback"""
    base_slides = [
        {
            'title': f'{topic}: Overview',
            'content': [
                f'Introduction to {topic}',
                'Key concepts and fundamentals',
                'Current state and trends',
                'Future outlook and opportunities'
            ],
            'slide_number': 1
        },
        {
            'title': f'Key Benefits of {topic}',
            'content': [
                'Improved efficiency and productivity',
                'Cost reduction and optimization',
                'Enhanced user experience',
                'Competitive advantages'
            ],
            'slide_number': 2
        },
        {
            'title': f'Implementation Strategy',
            'content': [
                'Planning and preparation phase',
                'Execution and deployment',
                'Monitoring and optimization',
                'Continuous improvement'
            ],
            'slide_number': 3
        },
        {
            'title': 'Conclusion and Next Steps',
            'content': [
                f'Summary of {topic} benefits',
                'Recommended action items',
                'Timeline and milestones',
                'Questions and discussion'
            ],
            'slide_number': 4
        }
    ]
    
    # Extend slides to match requested count
    while len(base_slides) < slide_count:
        slide_num = len(base_slides) + 1
        base_slides.append({
            'title': f'{topic}: Additional Insights {slide_num - 4}',
            'content': [
                'Key point and analysis',
                'Supporting evidence',
                'Practical applications',
                'Strategic implications'
            ],
            'slide_number': slide_num
        })
    
    return base_slides[:slide_count]

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Project Slidra',
        'version': '2.0.0'
    })

@app.route('/api/generate-slides', methods=['POST'])
def generate_slides():
    """Generate PowerPoint slides with bulletproof error handling"""
    try:
        # Get and validate request data with safe defaults
        data = request.get_json() or {}
        
        # Extract and validate input with defaults and safe handling
        topic = str(data.get('topic', 'Presentation Topic') or 'Presentation Topic').strip()
        tone = str(data.get('tone', 'professional') or 'professional').strip()
        mood = str(data.get('mood', 'modern') or 'modern').strip()
        slide_count = int(data.get('slide_count', 10) or 10)
        outline = str(data.get('outline', '') or '').strip()
        target_audience = str(data.get('target_audience', '') or '').strip()
        theme_name = str(data.get('theme_name', '') or '').strip()
        
        # Validate slide count
        if slide_count < 5 or slide_count > 20:
            slide_count = 10
        
        # Sanitize topic for filename
        topic = ''.join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).strip()
        if not topic:
            topic = 'Presentation Topic'

        # Generate slide content with bulletproof error handling
        slide_content, ai_enhanced = safe_ai_generation(
            topic=topic,
            tone=tone,
            outline=outline,
            target_audience=target_audience,
            slide_count=slide_count,
            mood=mood
        )

        # Get theme recommendation
        try:
            recommended_theme = theme_service.get_theme_recommendations(topic, tone, mood)
            if gemini_service.is_available:
                try:
                    gemini_theme = gemini_service.analyze_and_recommend_theme(
                        topic=topic, tone=tone, target_audience=target_audience, mood=mood
                    )
                    if gemini_theme and 'recommended_theme' in gemini_theme:
                        recommended_theme = gemini_theme['recommended_theme']
                except Exception as e:
                    print(f"Gemini theme recommendation failed: {e}")
        except Exception as e:
            print(f"Theme recommendation failed: {e}")
            recommended_theme = theme_service.get_default_theme()

        # Generate PowerPoint file
        try:
            temp_dir = tempfile.mkdtemp()
            file_id = str(uuid.uuid4())
            
            # Create professional filename
            clean_topic = ''.join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_topic = '_'.join(clean_topic.split())  # Replace spaces with underscores
            if len(clean_topic) > 50:  # Limit length for filesystem compatibility
                clean_topic = clean_topic[:50]
            
            professional_filename = f"AI_Generated_{clean_topic}_Presentation.pptx"
            filepath = os.path.join(temp_dir, professional_filename)
            
            # Create presentation
            pptx_generator.create_presentation(
                slide_content=slide_content,
                output_path=filepath,
                tone=tone,
                custom_theme=recommended_theme
            )
            
            # Store file info for later download
            temp_files[file_id] = {
                'path': filepath,
                'filename': professional_filename,
                'created': datetime.now()
            }

            # Create preview (first 3 slide titles)
            preview = []
            for slide in slide_content[:3]:
                preview.append(slide.get('title', 'Slide Title'))

            return jsonify({
                'success': True,
                'file_id': file_id,
                'filename': professional_filename,
                'slide_count': len(slide_content),
                'preview': preview,
                'ai_enhanced': ai_enhanced,
                'recommended_theme': recommended_theme,
                'theme_applied': True,
                'message': 'Your presentation has been generated successfully!'
            })

        except Exception as pptx_error:
            print(f"PowerPoint generation failed: {pptx_error}")
            # Emergency fallback - create basic presentation
            temp_dir = tempfile.mkdtemp()
            file_id = str(uuid.uuid4())
            
            # Create professional emergency filename
            clean_topic = ''.join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_topic = '_'.join(clean_topic.split())
            if len(clean_topic) > 50:
                clean_topic = clean_topic[:50]
            
            filepath = os.path.join(temp_dir, f"AI_Generated_{clean_topic}_Presentation.pptx")
            
            # Create emergency presentation
            emergency_slides = generate_basic_fallback_content(topic, tone, slide_count)
            pptx_generator.create_presentation(
                slide_content=emergency_slides,
                output_path=filepath,
                tone=tone
            )
            
            temp_files[file_id] = {
                'path': filepath,
                'filename': f"AI_Generated_{clean_topic}_Presentation.pptx",
                'created': datetime.now()
            }

            return jsonify({
                'success': True,
                'file_id': file_id,
                'filename': temp_files[file_id]['filename'],
                'slide_count': len(emergency_slides),
                'preview': [slide['title'] for slide in emergency_slides[:3]],
                'ai_enhanced': False,
                'theme_applied': True,
                'message': 'Your presentation has been generated successfully!'
            })

    except Exception as final_error:
        print(f"Emergency fallback failed: {final_error}")
        # Return error only as absolute last resort
        return jsonify({'error': 'Service temporarily unavailable. Please try again.'}), 503

@app.route('/api/download/<file_id>', methods=['GET'])
def download_file(file_id):
    """Download generated PowerPoint file"""
    try:
        if file_id not in temp_files:
            return jsonify({'error': 'File not found or expired'}), 404

        file_info = temp_files[file_id]

        if not os.path.exists(file_info['path']):
            return jsonify({'error': 'File not found'}), 404

        return send_file(
            file_info['path'],
            as_attachment=True,
            download_name=file_info['filename'],
            mimetype='application/vnd.openxmlformats-officedocument.presentationml.presentation'
        )

    except Exception as e:
        print(f"Error downloading file: {e}")
        return jsonify({'error': 'Failed to download file'}), 500

@app.route('/api/preview/<file_id>', methods=['GET'])
def preview_slides(file_id):
    """Get preview information about generated slides"""
    try:
        if file_id not in temp_files:
            return jsonify({'error': 'File not found or expired'}), 404

        return jsonify({
            'file_id': file_id,
            'filename': temp_files[file_id]['filename'],
            'created': temp_files[file_id]['created'].isoformat(),
            'status': 'ready'
        })

    except Exception as e:
        print(f"Error getting preview: {e}")
        return jsonify({'error': 'Failed to get preview'}), 500

@app.route('/api/themes', methods=['GET'])
def get_available_themes():
    """Get all available presentation themes"""
    try:
        themes = theme_service.get_all_themes()
        return jsonify({
            'success': True,
            'themes': themes
        })
    except Exception as e:
        print(f"Error getting themes: {e}")
        return jsonify({'error': 'Failed to get themes'}), 500

@app.route('/api/recommend-theme', methods=['POST'])
def recommend_theme():
    """Get theme recommendations based on content"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        topic = data.get('topic', '').strip()
        tone = data.get('tone', 'professional').strip()
        mood = data.get('mood', 'modern').strip()
        target_audience = data.get('target_audience', '').strip()

        if not topic:
            return jsonify({'error': 'Topic is required'}), 400

        # Get recommendations from theme service
        recommendations = theme_service.get_theme_recommendations(topic, tone, mood)

        # Try to get Gemini recommendations if available
        gemini_recommendation = None
        if gemini_service.is_available:
            try:
                theme_data = gemini_service.analyze_and_recommend_theme(
                    topic=topic,
                    tone=tone,
                    target_audience=target_audience,
                    mood=mood
                )
                gemini_recommendation = theme_data.get('recommended_theme')
            except Exception as e:
                print(f"Gemini theme recommendation failed: {e}")

        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'gemini_recommendation': gemini_recommendation,
            'ai_enhanced': gemini_recommendation is not None
        })

    except Exception as e:
        print(f"Error recommending theme: {e}")
        return jsonify({'error': 'Failed to recommend theme'}), 500

@app.route('/api/gemini-status', methods=['GET'])
def gemini_status():
    """Check Gemini AI service status"""
    return jsonify({
        'available': gemini_service.is_available,
        'model': gemini_service.model_name if gemini_service.is_available else None,
        'enabled': gemini_service.enabled
    })

if __name__ == '__main__':
    print("Starting Project Slidra server on 0.0.0.0:3000")
    app.run(debug=True, host='0.0.0.0', port=3000)
