#!/usr/bin/env python3
"""
Quality Assurance Service for Project Slidra
Implements comprehensive quality checks, content validation, and consistency verification
"""

from typing import List, Dict, Tuple, Optional
import re
import logging

class QualityAssuranceService:
    """Service for comprehensive presentation quality assurance"""
    
    def __init__(self):
        # Quality standards and thresholds
        self.quality_standards = {
            'content_depth': {
                'min_words_per_slide': 20,
                'min_bullet_points': 3,
                'max_bullet_points': 7,
                'min_words_per_bullet': 5
            },
            'consistency': {
                'title_format_consistency': 0.8,  # 80% consistency required
                'content_style_consistency': 0.85,
                'terminology_consistency': 0.9
            },
            'professional_standards': {
                'grammar_score_threshold': 85,
                'spelling_accuracy_threshold': 95,
                'readability_score_threshold': 70
            },
            'visual_standards': {
                'color_contrast_ratio': 4.5,  # WCAG AA standard
                'font_size_minimum': 18,
                'slide_balance_score': 70
            }
        }
        
        # Common presentation issues to check
        self.common_issues = {
            'content_issues': [
                'duplicate_content',
                'inconsistent_terminology',
                'poor_grammar',
                'spelling_errors',
                'insufficient_detail'
            ],
            'structure_issues': [
                'poor_flow',
                'missing_transitions',
                'unbalanced_slides',
                'inconsistent_formatting'
            ],
            'visual_issues': [
                'poor_contrast',
                'inconsistent_styling',
                'overcrowded_slides',
                'missing_visual_hierarchy'
            ]
        }
        
        # Professional terminology dictionary
        self.professional_terms = {
            'business': ['strategy', 'implementation', 'optimization', 'efficiency', 'ROI'],
            'technology': ['innovation', 'scalability', 'integration', 'architecture', 'deployment'],
            'healthcare': ['patient outcomes', 'clinical excellence', 'evidence-based', 'compliance'],
            'education': ['learning outcomes', 'pedagogical', 'curriculum', 'assessment', 'engagement']
        }

    def perform_comprehensive_qa(self, presentation_data: Dict) -> Dict:
        """Perform comprehensive quality assurance on a presentation"""
        slides = presentation_data.get('slides', [])
        metadata = presentation_data.get('metadata', {})
        
        # Initialize QA results
        qa_results = {
            'overall_score': 0,
            'quality_level': 'Needs Improvement',
            'content_quality': {},
            'consistency_check': {},
            'professional_standards': {},
            'visual_quality': {},
            'recommendations': [],
            'critical_issues': [],
            'warnings': []
        }
        
        # Perform individual quality checks
        qa_results['content_quality'] = self._assess_content_quality(slides)
        qa_results['consistency_check'] = self._check_consistency(slides)
        qa_results['professional_standards'] = self._verify_professional_standards(slides)
        qa_results['visual_quality'] = self._assess_visual_quality(slides, metadata)
        
        # Calculate overall score
        qa_results['overall_score'] = self._calculate_overall_score(qa_results)
        qa_results['quality_level'] = self._determine_quality_level(qa_results['overall_score'])
        
        # Generate recommendations
        qa_results['recommendations'] = self._generate_recommendations(qa_results)
        
        return qa_results

    def _assess_content_quality(self, slides: List[Dict]) -> Dict:
        """Assess the quality of content across all slides"""
        content_scores = []
        issues = []
        
        for i, slide in enumerate(slides):
            slide_score = self._assess_slide_content_quality(slide, i + 1)
            content_scores.append(slide_score['score'])
            issues.extend(slide_score['issues'])
        
        avg_score = sum(content_scores) / len(content_scores) if content_scores else 0
        
        return {
            'average_score': avg_score,
            'individual_scores': content_scores,
            'issues': issues,
            'depth_analysis': self._analyze_content_depth(slides),
            'clarity_score': self._assess_content_clarity(slides)
        }

    def _assess_slide_content_quality(self, slide: Dict, slide_number: int) -> Dict:
        """Assess content quality for a single slide"""
        title = slide.get('title', '')
        content = slide.get('content', [])
        
        score = 100
        issues = []
        
        # Check title quality
        if not title or len(title.split()) < 3:
            score -= 15
            issues.append(f"Slide {slide_number}: Title too brief or missing")
        
        # Check content depth
        if len(content) < self.quality_standards['content_depth']['min_bullet_points']:
            score -= 20
            issues.append(f"Slide {slide_number}: Insufficient bullet points")
        
        if len(content) > self.quality_standards['content_depth']['max_bullet_points']:
            score -= 10
            issues.append(f"Slide {slide_number}: Too many bullet points (may overwhelm audience)")
        
        # Check content detail
        total_words = sum(len(point.split()) for point in content)
        if total_words < self.quality_standards['content_depth']['min_words_per_slide']:
            score -= 25
            issues.append(f"Slide {slide_number}: Content too brief, lacks detail")
        
        # Check individual bullet point quality
        for j, point in enumerate(content):
            if len(point.split()) < self.quality_standards['content_depth']['min_words_per_bullet']:
                score -= 5
                issues.append(f"Slide {slide_number}, Point {j+1}: Bullet point too brief")
        
        return {'score': max(0, score), 'issues': issues}

    def _check_consistency(self, slides: List[Dict]) -> Dict:
        """Check consistency across all slides"""
        consistency_results = {
            'title_formatting': self._check_title_consistency(slides),
            'content_style': self._check_content_style_consistency(slides),
            'terminology': self._check_terminology_consistency(slides),
            'structure': self._check_structural_consistency(slides)
        }
        
        # Calculate overall consistency score
        scores = [result['score'] for result in consistency_results.values()]
        overall_score = sum(scores) / len(scores) if scores else 0
        
        consistency_results['overall_score'] = overall_score
        consistency_results['meets_standards'] = overall_score >= 80
        
        return consistency_results

    def _check_title_consistency(self, slides: List[Dict]) -> Dict:
        """Check title formatting consistency"""
        titles = [slide.get('title', '') for slide in slides]
        
        # Check capitalization patterns
        title_case_count = sum(1 for title in titles if self._is_title_case(title))
        sentence_case_count = sum(1 for title in titles if self._is_sentence_case(title))
        
        consistency_ratio = max(title_case_count, sentence_case_count) / len(titles) if titles else 0
        
        issues = []
        if consistency_ratio < self.quality_standards['consistency']['title_format_consistency']:
            issues.append("Inconsistent title capitalization across slides")
        
        return {
            'score': consistency_ratio * 100,
            'issues': issues,
            'title_case_count': title_case_count,
            'sentence_case_count': sentence_case_count
        }

    def _check_content_style_consistency(self, slides: List[Dict]) -> Dict:
        """Check content style consistency"""
        all_content = []
        for slide in slides:
            all_content.extend(slide.get('content', []))
        
        # Check bullet point formatting
        bullet_with_period = sum(1 for point in all_content if point.endswith('.'))
        bullet_without_period = len(all_content) - bullet_with_period
        
        consistency_ratio = max(bullet_with_period, bullet_without_period) / len(all_content) if all_content else 0
        
        issues = []
        if consistency_ratio < self.quality_standards['consistency']['content_style_consistency']:
            issues.append("Inconsistent bullet point punctuation")
        
        return {
            'score': consistency_ratio * 100,
            'issues': issues,
            'punctuation_consistency': consistency_ratio
        }

    def _check_terminology_consistency(self, slides: List[Dict]) -> Dict:
        """Check terminology consistency across slides"""
        all_text = []
        for slide in slides:
            all_text.append(slide.get('title', ''))
            all_text.extend(slide.get('content', []))
        
        combined_text = ' '.join(all_text).lower()
        
        # Check for consistent use of professional terms
        inconsistencies = []
        
        # Example: Check for consistent use of "utilize" vs "use"
        if 'utilize' in combined_text and 'use' in combined_text:
            inconsistencies.append("Mixed usage of 'utilize' and 'use'")
        
        # Check for consistent abbreviations
        if 'roi' in combined_text and 'return on investment' in combined_text:
            inconsistencies.append("Inconsistent use of ROI abbreviation")
        
        consistency_score = max(0, 100 - len(inconsistencies) * 20)
        
        return {
            'score': consistency_score,
            'issues': inconsistencies,
            'terminology_analysis': self._analyze_terminology_usage(combined_text)
        }

    def _verify_professional_standards(self, slides: List[Dict]) -> Dict:
        """Verify professional presentation standards"""
        standards_results = {
            'grammar_check': self._check_grammar_quality(slides),
            'spelling_check': self._check_spelling_accuracy(slides),
            'readability': self._assess_readability(slides),
            'professional_language': self._check_professional_language(slides)
        }
        
        # Calculate overall professional standards score
        scores = [result['score'] for result in standards_results.values()]
        overall_score = sum(scores) / len(scores) if scores else 0
        
        standards_results['overall_score'] = overall_score
        standards_results['meets_professional_standards'] = overall_score >= 85
        
        return standards_results

    def _assess_visual_quality(self, slides: List[Dict], metadata: Dict) -> Dict:
        """Assess visual quality and design standards"""
        visual_results = {
            'layout_balance': self._assess_layout_balance(slides),
            'visual_hierarchy': self._check_visual_hierarchy(slides),
            'color_usage': self._assess_color_usage(metadata),
            'typography': self._check_typography_standards(slides)
        }
        
        # Calculate overall visual quality score
        scores = [result['score'] for result in visual_results.values()]
        overall_score = sum(scores) / len(scores) if scores else 0
        
        visual_results['overall_score'] = overall_score
        visual_results['meets_visual_standards'] = overall_score >= 75
        
        return visual_results

    def _calculate_overall_score(self, qa_results: Dict) -> float:
        """Calculate overall presentation quality score"""
        weights = {
            'content_quality': 0.35,
            'consistency_check': 0.25,
            'professional_standards': 0.25,
            'visual_quality': 0.15
        }
        
        weighted_score = 0
        for category, weight in weights.items():
            if category in qa_results and 'average_score' in qa_results[category]:
                weighted_score += qa_results[category]['average_score'] * weight
            elif category in qa_results and 'overall_score' in qa_results[category]:
                weighted_score += qa_results[category]['overall_score'] * weight
        
        return weighted_score

    def _determine_quality_level(self, overall_score: float) -> str:
        """Determine quality level based on overall score"""
        if overall_score >= 90:
            return 'Excellent'
        elif overall_score >= 80:
            return 'Good'
        elif overall_score >= 70:
            return 'Satisfactory'
        elif overall_score >= 60:
            return 'Needs Improvement'
        else:
            return 'Poor'

    def _generate_recommendations(self, qa_results: Dict) -> List[str]:
        """Generate actionable recommendations based on QA results"""
        recommendations = []
        
        # Content recommendations
        if qa_results['content_quality']['average_score'] < 80:
            recommendations.append("Expand content depth with more detailed explanations and examples")
        
        # Consistency recommendations
        if qa_results['consistency_check']['overall_score'] < 80:
            recommendations.append("Improve consistency in formatting, terminology, and style")
        
        # Professional standards recommendations
        if qa_results['professional_standards']['overall_score'] < 85:
            recommendations.append("Enhance professional language and fix grammar/spelling issues")
        
        # Visual quality recommendations
        if qa_results['visual_quality']['overall_score'] < 75:
            recommendations.append("Improve visual design and layout balance")
        
        return recommendations

    # Helper methods for specific checks
    def _is_title_case(self, text: str) -> bool:
        """Check if text follows title case convention"""
        words = text.split()
        if not words:
            return False
        
        # First word should be capitalized
        if not words[0][0].isupper():
            return False
        
        # Check other words (excluding articles, prepositions, etc.)
        small_words = {'a', 'an', 'and', 'as', 'at', 'but', 'by', 'for', 'if', 'in', 'of', 'on', 'or', 'the', 'to', 'up'}
        
        for word in words[1:]:
            if word.lower() not in small_words and not word[0].isupper():
                return False
        
        return True

    def _is_sentence_case(self, text: str) -> bool:
        """Check if text follows sentence case convention"""
        return text and text[0].isupper() and not self._is_title_case(text)

    def _analyze_content_depth(self, slides: List[Dict]) -> Dict:
        """Analyze the depth and substance of content"""
        total_words = 0
        total_slides = len(slides)
        
        for slide in slides:
            content = slide.get('content', [])
            total_words += sum(len(point.split()) for point in content)
        
        avg_words_per_slide = total_words / total_slides if total_slides > 0 else 0
        
        depth_level = 'High' if avg_words_per_slide > 60 else 'Medium' if avg_words_per_slide > 30 else 'Low'
        
        return {
            'average_words_per_slide': avg_words_per_slide,
            'total_words': total_words,
            'depth_level': depth_level
        }

    def _assess_content_clarity(self, slides: List[Dict]) -> float:
        """Assess content clarity and readability"""
        clarity_score = 85  # Base score
        
        for slide in slides:
            content = slide.get('content', [])
            
            # Check for overly complex sentences
            for point in content:
                words = point.split()
                if len(words) > 25:  # Very long bullet point
                    clarity_score -= 5
                elif len(words) > 20:  # Long bullet point
                    clarity_score -= 2
        
        return max(0, clarity_score)

    def _check_grammar_quality(self, slides: List[Dict]) -> Dict:
        """Check grammar quality (simplified implementation)"""
        # This is a simplified grammar check
        # In a real implementation, you might use a grammar checking library
        
        grammar_score = 90  # Base score
        issues = []
        
        for slide in slides:
            title = slide.get('title', '')
            content = slide.get('content', [])
            
            # Check for common grammar issues
            all_text = [title] + content
            for text in all_text:
                # Check for double spaces
                if '  ' in text:
                    grammar_score -= 2
                    issues.append("Double spaces found")
                
                # Check for missing capitalization at start
                if text and not text[0].isupper():
                    grammar_score -= 3
                    issues.append("Missing capitalization")
        
        return {
            'score': max(0, grammar_score),
            'issues': issues
        }

    def _check_spelling_accuracy(self, slides: List[Dict]) -> Dict:
        """Check spelling accuracy (simplified implementation)"""
        # Simplified spelling check
        spelling_score = 95  # Base score
        issues = []
        
        # This would typically use a spell-checking library
        # For now, we'll do basic checks
        
        return {
            'score': spelling_score,
            'issues': issues
        }

    def _assess_readability(self, slides: List[Dict]) -> Dict:
        """Assess readability of content"""
        readability_score = 80  # Base score
        
        for slide in slides:
            content = slide.get('content', [])
            
            # Simple readability metrics
            for point in content:
                words = point.split()
                avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
                
                # Penalize overly complex vocabulary
                if avg_word_length > 7:
                    readability_score -= 5
        
        return {
            'score': max(0, readability_score),
            'reading_level': 'Professional'
        }

    def _check_professional_language(self, slides: List[Dict]) -> Dict:
        """Check for professional language usage"""
        professional_score = 85  # Base score
        issues = []
        
        casual_words = ['really', 'very', 'a lot', 'big', 'small', 'get', 'make', 'stuff', 'things']
        
        for slide in slides:
            all_text = slide.get('title', '') + ' ' + ' '.join(slide.get('content', []))
            text_lower = all_text.lower()
            
            for casual_word in casual_words:
                if casual_word in text_lower:
                    professional_score -= 3
                    issues.append(f"Casual language detected: '{casual_word}'")
        
        return {
            'score': max(0, professional_score),
            'issues': issues
        }

    def _assess_layout_balance(self, slides: List[Dict]) -> Dict:
        """Assess layout balance and composition"""
        balance_score = 80  # Base score
        
        for slide in slides:
            content = slide.get('content', [])
            
            # Check for overcrowded slides
            if len(content) > 7:
                balance_score -= 10
            elif len(content) > 5:
                balance_score -= 5
        
        return {
            'score': max(0, balance_score),
            'balance_level': 'Good'
        }

    def _check_visual_hierarchy(self, slides: List[Dict]) -> Dict:
        """Check visual hierarchy implementation"""
        hierarchy_score = 85  # Base score
        
        # This would check actual visual elements in a real implementation
        # For now, we'll assess based on content structure
        
        return {
            'score': hierarchy_score,
            'hierarchy_level': 'Clear'
        }

    def _assess_color_usage(self, metadata: Dict) -> Dict:
        """Assess color usage and contrast"""
        color_score = 90  # Base score
        
        # This would analyze actual color usage in a real implementation
        
        return {
            'score': color_score,
            'contrast_ratio': 4.8,
            'accessibility_compliant': True
        }

    def _check_typography_standards(self, slides: List[Dict]) -> Dict:
        """Check typography standards"""
        typography_score = 85  # Base score
        
        # This would check actual font usage in a real implementation
        
        return {
            'score': typography_score,
            'font_consistency': True,
            'readability': 'High'
        }

    def _analyze_terminology_usage(self, text: str) -> Dict:
        """Analyze terminology usage patterns"""
        # Simple analysis of professional terminology
        professional_term_count = 0
        
        for category, terms in self.professional_terms.items():
            for term in terms:
                if term in text:
                    professional_term_count += 1
        
        return {
            'professional_terms_used': professional_term_count,
            'terminology_level': 'Professional' if professional_term_count > 5 else 'Standard'
        }

    def _check_structural_consistency(self, slides: List[Dict]) -> Dict:
        """Check structural consistency across slides"""
        structure_score = 85  # Base score
        
        # Check for consistent slide structure
        slide_lengths = [len(slide.get('content', [])) for slide in slides]
        
        # Calculate variance in slide lengths
        if slide_lengths:
            avg_length = sum(slide_lengths) / len(slide_lengths)
            variance = sum((length - avg_length) ** 2 for length in slide_lengths) / len(slide_lengths)
            
            # High variance indicates inconsistent structure
            if variance > 4:
                structure_score -= 15
            elif variance > 2:
                structure_score -= 8
        
        return {
            'score': max(0, structure_score),
            'structure_variance': variance if 'variance' in locals() else 0
        }
