#!/usr/bin/env python3
"""
SlideForge AI - PowerPoint Generator
Entry point for the Flask application
"""

import os
from dotenv import load_dotenv
from app import app

# Load environment variables
load_dotenv()

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

    print(f"Starting SlideForge AI server on {host}:{port}")
    app.run(host=host, port=port, debug=debug)
